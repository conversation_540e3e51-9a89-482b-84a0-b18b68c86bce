# 充电桩中心系统通信协议

该项目实现了充电桩与中心系统之间的TCP/IP Socket通信协议，支持高并发连接，按照指定的二进制报文格式进行解析和封装。

## 系统架构

系统由以下几个主要模块组成：

- **server.py**: 中心系统服务器，支持多个充电桩同时连接，处理充电桩发送的命令并回复
- **protocol.py**: 通信协议模块，实现报文的解析和封装
- **logger.py**: 日志模块，按天记录通信报文
- **client_simulator.py**: 充电桩客户端模拟器，用于测试服务器功能

## 通信协议

通信协议采用二进制格式，报文格式如下：

1. 起始域：2 Byte，固定值为 0xFA 0xF5
2. 长度域：1 Byte，取值范围为 0～0xFF（即 0～255）
3. 版本域：1 Byte，固定值为 0x80
4. 序列号域：1 Byte，取值范围为 0～0xFF（即 0～255）
5. 命令代码：1 Byte
6. 数据域：N Byte，具体内容参考命令的详细说明
7. 校验和域：1 Byte

## 支持的命令

- **0x09**: 充电桩签到命令
- **0x10**: 签到响应命令

## 使用方法

### 启动服务器

```bash
python server.py
```

### 启动客户端模拟器

```bash
python client_simulator.py -n <客户端数量> -H <服务器地址> -p <服务器端口>
```

参数说明：
- `-n, --num-clients`: 客户端数量，默认为1
- `-H, --host`: 服务器地址，默认为127.0.0.1
- `-p, --port`: 服务器端口，默认为8080

## 系统特点

- 支持约5000台充电桩同时连接
- 高并发处理能力
- 按天记录日志
- 自动维护连接状态
- 报文校验和验证
- 异步IO实现高性能