#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
客户端连接处理器

处理客户端连接、数据接收和断开连接的逻辑。
"""

import asyncio
from typing import Dict, Optional

from messages import BaseMessage
from logger import setup_logger

# 创建日志记录器
logger = setup_logger('handler_info')

async def handle_client(reader: asyncio.StreamReader, writer: asyncio.StreamWriter, 
                       clients: Dict[str, asyncio.Transport], running_ref: bool,
                       handle_command_func):
    """
    处理客户端连接
    
    Args:
        reader: 用于读取客户端数据的StreamReader
        writer: 用于向客户端写入数据的StreamWriter
        clients: 客户端连接字典
        running_ref: 服务器运行状态
        handle_command_func: 处理命令的函数
    """
    # 获取客户端地址
    addr = writer.get_extra_info('peername')
    client_id = f"{addr[0]}:{addr[1]}"
    
    # 保存客户端连接
    clients[client_id] = writer
    logger.info(f"新客户端连接: {client_id}")
    
    try:
        while running_ref:
            # 读取起始域
            start_bytes = await reader.readexactly(2)
            if start_bytes != bytes([0xFA, 0xF5]):
                logger.warning(f"客户端 {client_id} 发送了无效的起始域: {start_bytes.hex()}")
                continue
            
            # 读取长度域
            length_byte = await reader.readexactly(1)
            length = length_byte[0]
            
            # 读取剩余的报文
            remaining_data = await reader.readexactly(length+1)
            
            # 完整的报文
            message = start_bytes + length_byte + remaining_data
            
            # 记录接收到的报文
            logger.info(f"从客户端 {client_id} 接收到报文: {message.hex()}")
            
            # 解析报文
            try:
                # 获取命令代码
                command_code = message[5] if len(message) > 5 else None
                
                # 根据命令代码获取对应的报文类
                message_class = BaseMessage.get_message_class(command_code)
                
                if message_class:
                    # 解码报文
                    parsed_message = message_class.decode(message)
                    if parsed_message:
                        # 处理命令
                        response = await handle_command_func(parsed_message, client_id)
                        if response:  # 只有当有响应时才发送
                            # 检查响应是单个报文还是多个报文列表
                            if isinstance(response, list):
                                # 发送多个响应报文
                                for resp in response:
                                    writer.write(resp)
                                    await writer.drain()
                                    logger.info(f"向客户端 {client_id} 发送响应: {resp.hex()}")
                            else:
                                # 发送单个响应报文
                                writer.write(response)
                                await writer.drain()
                                logger.info(f"向客户端 {client_id} 发送响应: {response.hex()}")
            except Exception as e:
                logger.error(f"处理客户端 {client_id} 报文时出错: {e}")
    
    except asyncio.IncompleteReadError:
        # 客户端断开连接
        logger.info(f"客户端 {client_id} 断开连接")
    except Exception as e:
        logger.error(f"处理客户端 {client_id} 连接时出错: {e}")
    finally:
        # 关闭连接并从客户端列表中移除
        writer.close()
        try:
            await writer.wait_closed()
        except:
            pass
        
        if client_id in clients:
            del clients[client_id]