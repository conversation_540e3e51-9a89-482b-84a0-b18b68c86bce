#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
命令处理器

处理客户端发送的各种命令并生成响应。
"""

from datetime import datetime
from typing import Optional, List, Union

from messages import BaseMessage, SignInMessage, SignInResponseMessage, CommunicationModeResponse, TimeSyncResponse, Command, CommunicationMode, TimeSync, HeartBeat, HeartBeatResponse, OperationParamsMessage, OperationParamsResponseMessage, UserAuthorization, UserAuthorizationResponse
from logger import setup_logger

# 创建日志记录器
# logger = setup_logger('handler_info')
import logging
logger = logging.getLogger("handler_info")

async def handle_command(message: BaseMessage, client_id: str) -> Optional[Union[bytes, List[bytes]]]:
    """
    处理客户端命令
    
    Args:
        message: 解析后的报文对象
        client_id: 客户端ID
        
    Returns:
        响应报文的字节数据，如果不需要响应则返回None
    """
    # 获取命令信息
    command_code = message.COMMAND_CODE
    command = None
    
    # 查找对应的命令枚举
    for cmd in Command:
        if cmd.value == command_code:
            command = cmd
            break
    
    # 记录命令信息
    if command:
        logger.info(f"收到命令: {command.description}，客户端ID: {client_id}，命令代码: 0x{command_code:02X}")
    else:
    # if not command:
        logger.info(f"<- 收到未知命令，客户端ID: {client_id}，命令代码: 0x{command_code:02X}")
        return None
    
    # 如果命令不需要回复，则只记录不回复
    #if not (command and command.need_response):
        # return None

    if isinstance(message, SignInMessage):  # 0x10 充电桩签到命令
        # 创建响应列表
        charger_id = message.charging_pile_code.hex()
        logger.info(f"<-- 收到充电桩: {charger_id}，命令代码: 0x{command_code:02X}，{command.description}")
        responses = []
        
        # 向平台上报充电桩连接
        try:
            # 创建gRPC客户端通知平台设备已连接
            import grpc
            import protos.charger_service_pb2 as charger_pb2
            import protos.charger_service_pb2_grpc as charger_pb2_grpc
            
            # 创建gRPC通道 (假设服务器在本地运行)
            with grpc.insecure_channel('localhost:50051') as channel:
                # 创建Stub
                stub = charger_pb2_grpc.ChargerServiceStub(channel)
                
                # 创建设备签到通知请求
                request = charger_pb2.DeviceSignInNotification(
                    charger_id=charger_id,
                    device_code=charger_id,  # 使用充电桩编码作为设备编码
                    status=1  # 1表示在线
                )
                
                # 发送通知
                response = stub.DeviceSignIn(request)
                
                if response.success:
                    logger.info(f"成功向平台通知设备 {charger_id} 已连接")
                else:
                    logger.warning(f"向平台通知设备 {charger_id} 已连接失败: {response.message}")
        except Exception as e:
            logger.error(f"向平台通知设备 {charger_id} 已连接出错: {e}")
        
        # 1. 处理充电桩签到命令，返回0x09响应
        sign_in_response = SignInResponseMessage()
        sign_in_response.set_charging_pile_code(message.charging_pile_code)
        
        # 设置电价信息
        price_service_version = 1  # 示例电价服务费版本号
        sign_in_response.set_price_service_version(price_service_version)

        price_tiers = [
            {'start_time': '00:00', 'end_time': '17:59', 'price': 50},  # 谷时段
        ]
        sign_in_response.set_price_info(price_tiers)
        
        # 设置服务费信息
        service_fee_tiers = [
            {'start_time': '00:00', 'end_time': '17:59', 'price': 10}  # 全天固定服务费
        ]
        sign_in_response.set_service_fee_info(service_fee_tiers)
        
        # 添加签到响应报文
        responses.append(sign_in_response.encode())
        logger.info(
            f"-> 向充电桩 {message.charging_pile_code.hex()} 发送： 0x{sign_in_response.COMMAND_CODE:02X}")
        
        # 2. 创建并发送通信模式设置命令 0xE0
        commu_mode = CommunicationMode()
        commu_mode.set_communication_info( b'\x01', 15)  # 设置通信模式为1，上报间隔为15秒
        responses.append(commu_mode.encode())
        logger.info(f"--> 向充电桩 {message.charging_pile_code.hex()} 发送： 0x{commu_mode.COMMAND_CODE:02X} ,通信模式设置命令 {commu_mode.communication_mode}")
        
        # 3. 创建并发送时间同步命令 0x42
        time_sync = TimeSync()
        time_sync.set_info(0, 0)  # 设置用户ID和指令序号
        responses.append(time_sync.encode()) # encode中获取当前时间
        formatted_time = "{:04d}-{:02d}-{:02d} {:02d}:{:02d}:{:02d}".format(
            time_sync.year, time_sync.month, time_sync.day, time_sync.hour, time_sync.minute,time_sync.second)
        logger.info(f"--> 向充电桩 {message.charging_pile_code.hex()} 发送：0x{time_sync.COMMAND_CODE:02X}, 发送时间{formatted_time} 同步命令")

        # 4. 创建并发送设置公司信息 0x0A
        company_set = OperationParamsMessage()
        #company_set.set_basic_info(0, 0)  # 设置用户ID和指令序号
        company_name = "安徽卓越电气有限公司"
        company_set.set_operation_info(1, company_name)
        responses.append((company_set.encode()))

        logger.info(
            f"--> 向充电桩 {message.charging_pile_code.hex()} 发送：0x{company_set.COMMAND_CODE:02X}, 设置参数: {company_set.param_type }为:{company_set.param_content}")
        # 返回所有响应报文
        return responses
    
    elif isinstance(message, CommunicationModeResponse):  # 0xF0 通信模式设置命令
        # 处理接受到0xF0，记录详细信息
        logger.info(f"<- 设备{client_id},回复0x{command_code: 02X}，{command.description}，设置: {message.communication_mode.hex()}, 上报间隔: {message.interval}秒")
        return None

    elif isinstance(message, TimeSyncResponse):
        formatted_time = "{:04d}-{:02d}-{:02d} {:02d}:{:02d}:{:02d}".format(
            message.year, message.month, message.day, message.hour, message.minute, message.second)
        logger.info(
            f"<- 设备{client_id},回复0x{command_code:02X}，{command.description}#，设置时间: {formatted_time}")
        return None

    if isinstance(message, HeartBeat):  # 0x58 充电桩签到命令
        # 创建响应列表
        logger.info(
            f"<-- 收到充电桩: {client_id}，命令代码: 0x{command_code:02X}，{command.description}")
        # 处理充电桩签到命令，返回0x48响应
        heartbeat_response = HeartBeatResponse()
        heartbeat_response.set_heartbeat(message.heartbeat)
        logger.info(
            f"-> 向充电桩 {client_id} 发送： 0x{heartbeat_response.COMMAND_CODE:02X}")
        return heartbeat_response.encode()

    elif isinstance(message, OperationParamsResponseMessage):  # 0x1A 运营参数设置响应命令
        # 处理接收到0x1A，记录详细信息
        param_name = message.get_param_type_description()
        if message.result == 0: #表示成功
            logger.info(f"<- 设备{client_id},回复0x{command_code:02X}，设置参数: {param_name }为{message.param_content}， 成功")
        if message.result == 1: #表示失败
            logger.info(f"<- 设备{client_id},回复0x{command_code:02X}，设置参数: {param_name }为{message.param_content}， 失败Fail")
        return None
    
    elif isinstance(message, UserAuthorization):  # 0x6C 中心系统下达合法用户认证通过信息
        # 处理接收到0x6C，记录详细信息
        logger.info(f"<- 设备{client_id},收到0x{command_code:02X}，{command.description}，卡号: {message.card_number}")
        
        # 创建用户认证响应报文
        auth_response = UserAuthorizationResponse()
        auth_response.set_auth_info(
            user_id=message.user_id,
            command_sequence=message.command_sequence,
            connector_number=message.connector_number,
            card_number=message.card_number,
            result=0  # 0表示成功
        )
        
        logger.info(f"-> 向设备{client_id}发送: 0x{auth_response.COMMAND_CODE:02X}，用户认证响应")
        return auth_response.encode()
    
    elif isinstance(message, UserAuthorizationResponse):  # 0x7A 充电桩应答中心合法用户认证通过信息
        # 处理接收到0x7A，记录详细信息
        result_str = "成功" if message.result == 0 else "失败"
        logger.info(f"<- 设备{client_id},回复0x{command_code:02X}，{command.description}，卡号: {message.card_number}，认证结果: {result_str}")
        return None
    
    # 其他命令处理...
    
    return None