#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
服务器关闭处理器

处理服务器关闭的逻辑，包括关闭所有客户端连接和服务器本身。
"""

import asyncio
from typing import Dict

from logger import setup_logger

# 创建日志记录器
#logger = setup_logger('service_info')
import logging
logger = logging.getLogger("service_info")

# 关闭状态标志，用于防止重复调用
_shutdown_in_progress = False

#async def shutdown(clients: Dict[str, asyncio.StreamWriter], server):
async def shutdown(clients: Dict[str, asyncio.Transport], server):
    """
    关闭服务器
    
    Args:
        clients: 客户端连接字典
        server: 服务器实例
    """
    global _shutdown_in_progress
    
    # 检查是否已经在关闭过程中
    if _shutdown_in_progress:
        return
    
    # 设置关闭标志
    _shutdown_in_progress = True
    
    try:
        # 记录关闭过程开始
        logger.info("<----- 开始关闭服务器... ----->")
        
        # 关闭所有客户端连接
        for client_id, transport in list(clients.items()):
            transport.close()
            
        # 关闭服务器
        if server:
            server.close()
            await server.wait_closed()
            
        logger.info("<===== 服务器已关闭 =====>")
    finally:
        # 重置关闭标志，以便下次可以正常关闭
        # 注意：在实际应用中，这个重置可能不需要，因为服务器关闭后通常不会再启动
        # 但为了完整性和可能的测试场景，我们在这里重置它
        _shutdown_in_progress = False