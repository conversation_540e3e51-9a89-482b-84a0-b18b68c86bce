#!/bin/bash
# -*- coding: utf-8 -*-

# 充电桩中心系统服务器运行脚本
# 该脚本用于启动、停止、重启和查看充电桩中心系统服务器状态

# 设置默认值
PROJECT_DIR="$(cd "$(dirname "$0")" && pwd)"
PID_FILE="${PROJECT_DIR}/server.pid"
LOG_DIR="${PROJECT_DIR}/logs"
HOST="0.0.0.0"
PORT=8080
DEBUG=false

# 颜色定义
RED="\033[0;31m"
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
BLUE="\033[0;34m"
NC="\033[0m" # No Color

# 帮助信息
show_help() {
    echo -e "${BLUE}充电桩中心系统服务器运行脚本${NC}"
    echo ""
    echo "用法: $0 [命令] [选项]"
    echo ""
    echo "命令:"
    echo "  start       启动服务器"
    echo "  stop        停止服务器"
    echo "  restart     重启服务器"
    echo "  status      查看服务器状态"
    echo ""
    echo "选项:"
    echo "  -h, --help        显示帮助信息"
    echo "  -H, --host HOST   设置服务器监听地址 (默认: 0.0.0.0)"
    echo "  -p, --port PORT   设置服务器监听端口 (默认: 8080)"
    echo "  -l, --log-dir DIR 设置日志目录 (默认: ./logs)"
    echo "  -d, --debug       启用调试模式"
    echo ""
    echo "示例:"
    echo "  $0 start -p 9090 -d     在9090端口启动服务器并开启调试模式"
    echo "  $0 stop                 停止服务器"
    echo "  $0 status               查看服务器状态"
}

# 检查Python环境
check_python() {
    if ! command -v python3 &> /dev/null; then
        echo -e "${RED}错误: 未找到python3命令${NC}"
        echo "请安装Python 3.6或更高版本"
        exit 1
    fi
    
    # 检查uvloop是否安装
    if ! python3 -c "import uvloop" &> /dev/null; then
        echo -e "${YELLOW}警告: 未安装uvloop${NC}"
        echo -e "建议安装uvloop以提升性能: ${GREEN}pip install uvloop${NC}"
    fi
}

# 检查服务器是否正在运行
is_running() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null; then
            return 0 # 正在运行
        fi
    fi
    return 1 # 未运行
}

# 启动服务器
start_server() {
    if is_running; then
        echo -e "${YELLOW}服务器已经在运行中 (PID: $(cat $PID_FILE))${NC}"
        return 1
    fi
    
    # 确保日志目录存在
    mkdir -p "$LOG_DIR"
    
    # 构建启动命令
    local cmd="python3 ${PROJECT_DIR}/main.py -H $HOST -p $PORT -l $LOG_DIR"
    if [ "$DEBUG" = true ]; then
        cmd="$cmd -d"
    fi
    
    # 启动服务器并将PID保存到文件
    echo -e "${GREEN}正在启动服务器...${NC}"
    echo -e "监听地址: ${BLUE}$HOST:$PORT${NC}"
    echo -e "日志目录: ${BLUE}$LOG_DIR${NC}"
    if [ "$DEBUG" = true ]; then
        echo -e "调试模式: ${GREEN}已启用${NC}"
    fi
    
    nohup $cmd > "$LOG_DIR/server_stdout.log" 2> "$LOG_DIR/server_stderr.log" & 
    local pid=$!
    echo $pid > "$PID_FILE"
    
    # 等待一会儿，检查服务器是否成功启动
    sleep 2
    if ps -p "$pid" > /dev/null; then
        echo -e "${GREEN}服务器已成功启动 (PID: $pid)${NC}"
        return 0
    else
        echo -e "${RED}服务器启动失败，请检查日志文件${NC}"
        echo -e "日志文件: ${BLUE}$LOG_DIR/server_stderr.log${NC}"
        rm -f "$PID_FILE"
        return 1
    fi
}

# 停止服务器
stop_server() {
    if ! is_running; then
        echo -e "${YELLOW}服务器未在运行${NC}"
        return 1
    fi
    
    local pid=$(cat "$PID_FILE")
    echo -e "${YELLOW}正在停止服务器 (PID: $pid)...${NC}"
    
    # 先尝试优雅地停止服务器 (SIGTERM)
    kill -15 "$pid" 2>/dev/null
    
    # 等待服务器停止
    local timeout=30
    local count=0
    while ps -p "$pid" > /dev/null; do
        sleep 1
        count=$((count + 1))
        if [ $count -ge $timeout ]; then
            echo -e "${RED}服务器未能在 ${timeout} 秒内停止，正在强制终止...${NC}"
            kill -9 "$pid" 2>/dev/null
            break
        fi
    done
    
    # 删除PID文件
    rm -f "$PID_FILE"
    echo -e "${GREEN}服务器已停止${NC}"
    return 0
}

# 重启服务器
restart_server() {
    echo -e "${BLUE}正在重启服务器...${NC}"
    stop_server
    sleep 2
    start_server
}

# 查看服务器状态
show_status() {
    if is_running; then
        local pid=$(cat "$PID_FILE")
        echo -e "${GREEN}服务器状态: 运行中${NC}"
        echo -e "PID: ${BLUE}$pid${NC}"
        echo -e "运行时间: ${BLUE}$(ps -o etime= -p $pid)${NC}"
        echo -e "CPU使用率: ${BLUE}$(ps -o %cpu= -p $pid)%${NC}"
        echo -e "内存使用: ${BLUE}$(ps -o %mem= -p $pid)%${NC}"
        
        # 显示监听端口
        local ports=$(lsof -Pan -p $pid -i tcp 2>/dev/null | grep LISTEN | awk '{print $9}' | cut -d: -f2)
        if [ -n "$ports" ]; then
            echo -e "监听端口: ${BLUE}$ports${NC}"
        fi
    else
        echo -e "${YELLOW}服务器状态: 未运行${NC}"
    fi
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -H|--host)
                HOST="$2"
                shift 2
                ;;
            -p|--port)
                PORT="$2"
                shift 2
                ;;
            -l|--log-dir)
                LOG_DIR="$2"
                shift 2
                ;;
            -d|--debug)
                DEBUG=true
                shift
                ;;
            start|stop|restart|status)
                COMMAND="$1"
                shift
                ;;
            *)
                echo -e "${RED}未知选项: $1${NC}"
                show_help
                exit 1
                ;;
        esac
    done
}

# 主函数
main() {
    # 检查Python环境
    check_python
    
    # 如果没有命令，显示帮助
    if [ -z "$COMMAND" ]; then
        show_help
        exit 1
    fi
    
    # 执行命令
    case "$COMMAND" in
        start)
            start_server
            ;;
        stop)
            stop_server
            ;;
        restart)
            restart_server
            ;;
        status)
            show_status
            ;;
        *)
            echo -e "${RED}未知命令: $COMMAND${NC}"
            show_help
            exit 1
            ;;
    esac
}

# 解析命令行参数
COMMAND=""
parse_args "$@"

# 执行主函数
main