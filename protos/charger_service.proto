syntax = "proto3";

package charger;

// 远程启动请求消息
message RemoteStartRequest {
  string charger_id = 1;        // 充电桩ID
  int32 connector_number = 2;    // 充电端口号
  string card_number = 3;        // 充电卡号
  string process_seq = 4;        // 流程序号
  string customer_id = 5;        // 客户号(车辆VIN，车牌号，公司缩写)
  double fee_balance = 6;        // 电费主账户余额
  double fee_real_balance = 7;   // 电费主账户可用余额
  double service_balance = 8;    // 服务费账户余额
  double service_real_balance = 9; // 服务费账户可用余额
  string center_serial = 10;     // 中心交易流水
  string boss_serial = 11;       // BOSS出单机构流水号
  int32 charging_type = 12;      // 充电方式
  int32 charging_mode = 13;      // 充电模式
  int32 charging_parameter = 14; // 充电参数
}

// 远程启动响应消息
message RemoteStartResponse {
  bool success = 1;              // 操作是否成功
  string message = 2;            // 响应消息
  string charger_id = 3;         // 充电桩ID
}

// 设置二维码请求消息
message SetQRCodeRequest {
  string charger_id = 1;     // 充电桩ID
  string qr_code = 2;        // 二维码内容
}

// 设置二维码响应消息
message SetQRCodeResponse {
  bool success = 1;          // 操作是否成功
  string message = 2;        // 响应消息
  string charger_id = 3;     // 充电桩ID
}

// 设备签到通知消息
message DeviceSignInNotification {
  string charger_id = 1;     // 充电桩ID
  string device_code = 2;    // 设备编码
  int32 status = 3;          // 设备状态
}

// 设备签到通知响应消息
message DeviceSignInResponse {
  bool success = 1;          // 操作是否成功
  string message = 2;        // 响应消息
}

// 充电桩服务
service ChargerService {
  // 远程启动充电桩
  rpc RemoteStart(RemoteStartRequest) returns (RemoteStartResponse) {}
  // 设置二维码
  rpc SetQRCode(SetQRCodeRequest) returns (SetQRCodeResponse) {}
  // 设备签到通知
  rpc DeviceSignIn(DeviceSignInNotification) returns (DeviceSignInResponse) {}
}