# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

import charger_service_pb2 as charger__service__pb2


class ChargerServiceStub(object):
    """充电桩服务
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.RemoteStart = channel.unary_unary(
                '/charger.ChargerService/RemoteStart',
                request_serializer=charger__service__pb2.RemoteStartRequest.SerializeToString,
                response_deserializer=charger__service__pb2.RemoteStartResponse.FromString,
                )
        self.SetQRCode = channel.unary_unary(
                '/charger.ChargerService/SetQRCode',
                request_serializer=charger__service__pb2.SetQRCodeRequest.SerializeToString,
                response_deserializer=charger__service__pb2.SetQRCodeResponse.FromString,
                )
        self.DeviceSignIn = channel.unary_unary(
                '/charger.ChargerService/DeviceSignIn',
                request_serializer=charger__service__pb2.DeviceSignInNotification.SerializeToString,
                response_deserializer=charger__service__pb2.DeviceSignInResponse.FromString,
                )


class ChargerServiceServicer(object):
    """充电桩服务
    """

    def RemoteStart(self, request, context):
        """远程启动充电桩
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetQRCode(self, request, context):
        """设置二维码
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeviceSignIn(self, request, context):
        """设备签到通知
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ChargerServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'RemoteStart': grpc.unary_unary_rpc_method_handler(
                    servicer.RemoteStart,
                    request_deserializer=charger__service__pb2.RemoteStartRequest.FromString,
                    response_serializer=charger__service__pb2.RemoteStartResponse.SerializeToString,
            ),
            'SetQRCode': grpc.unary_unary_rpc_method_handler(
                    servicer.SetQRCode,
                    request_deserializer=charger__service__pb2.SetQRCodeRequest.FromString,
                    response_serializer=charger__service__pb2.SetQRCodeResponse.SerializeToString,
            ),
            'DeviceSignIn': grpc.unary_unary_rpc_method_handler(
                    servicer.DeviceSignIn,
                    request_deserializer=charger__service__pb2.DeviceSignInNotification.FromString,
                    response_serializer=charger__service__pb2.DeviceSignInResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'charger.ChargerService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class ChargerService(object):
    """充电桩服务
    """

    @staticmethod
    def RemoteStart(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/charger.ChargerService/RemoteStart',
            charger__service__pb2.RemoteStartRequest.SerializeToString,
            charger__service__pb2.RemoteStartResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetQRCode(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/charger.ChargerService/SetQRCode',
            charger__service__pb2.SetQRCodeRequest.SerializeToString,
            charger__service__pb2.SetQRCodeResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DeviceSignIn(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/charger.ChargerService/DeviceSignIn',
            charger__service__pb2.DeviceSignInNotification.SerializeToString,
            charger__service__pb2.DeviceSignInResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
