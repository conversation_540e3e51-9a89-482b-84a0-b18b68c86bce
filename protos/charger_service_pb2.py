# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: charger_service.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x15\x63harger_service.proto\x12\x07\x63harger\"\xdd\x02\n\x12RemoteStartRequest\x12\x12\n\ncharger_id\x18\x01 \x01(\t\x12\x18\n\x10\x63onnector_number\x18\x02 \x01(\x05\x12\x13\n\x0b\x63\x61rd_number\x18\x03 \x01(\t\x12\x13\n\x0bprocess_seq\x18\x04 \x01(\t\x12\x13\n\x0b\x63ustomer_id\x18\x05 \x01(\t\x12\x13\n\x0b\x66\x65\x65_balance\x18\x06 \x01(\x01\x12\x18\n\x10\x66\x65\x65_real_balance\x18\x07 \x01(\x01\x12\x17\n\x0fservice_balance\x18\x08 \x01(\x01\x12\x1c\n\x14service_real_balance\x18\t \x01(\x01\x12\x15\n\rcenter_serial\x18\n \x01(\t\x12\x13\n\x0b\x62oss_serial\x18\x0b \x01(\t\x12\x15\n\rcharging_type\x18\x0c \x01(\x05\x12\x15\n\rcharging_mode\x18\r \x01(\x05\x12\x1a\n\x12\x63harging_parameter\x18\x0e \x01(\x05\"K\n\x13RemoteStartResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x12\n\ncharger_id\x18\x03 \x01(\t\"7\n\x10SetQRCodeRequest\x12\x12\n\ncharger_id\x18\x01 \x01(\t\x12\x0f\n\x07qr_code\x18\x02 \x01(\t\"I\n\x11SetQRCodeResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x12\n\ncharger_id\x18\x03 \x01(\t\"S\n\x18\x44\x65viceSignInNotification\x12\x12\n\ncharger_id\x18\x01 \x01(\t\x12\x13\n\x0b\x64\x65vice_code\x18\x02 \x01(\t\x12\x0e\n\x06status\x18\x03 \x01(\x05\"8\n\x14\x44\x65viceSignInResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t2\xf6\x01\n\x0e\x43hargerService\x12J\n\x0bRemoteStart\x12\x1b.charger.RemoteStartRequest\x1a\x1c.charger.RemoteStartResponse\"\x00\x12\x44\n\tSetQRCode\x12\x19.charger.SetQRCodeRequest\x1a\x1a.charger.SetQRCodeResponse\"\x00\x12R\n\x0c\x44\x65viceSignIn\x12!.charger.DeviceSignInNotification\x1a\x1d.charger.DeviceSignInResponse\"\x00\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'charger_service_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_REMOTESTARTREQUEST']._serialized_start=35
  _globals['_REMOTESTARTREQUEST']._serialized_end=384
  _globals['_REMOTESTARTRESPONSE']._serialized_start=386
  _globals['_REMOTESTARTRESPONSE']._serialized_end=461
  _globals['_SETQRCODEREQUEST']._serialized_start=463
  _globals['_SETQRCODEREQUEST']._serialized_end=518
  _globals['_SETQRCODERESPONSE']._serialized_start=520
  _globals['_SETQRCODERESPONSE']._serialized_end=593
  _globals['_DEVICESIGNINNOTIFICATION']._serialized_start=595
  _globals['_DEVICESIGNINNOTIFICATION']._serialized_end=678
  _globals['_DEVICESIGNINRESPONSE']._serialized_start=680
  _globals['_DEVICESIGNINRESPONSE']._serialized_end=736
  _globals['_CHARGERSERVICE']._serialized_start=739
  _globals['_CHARGERSERVICE']._serialized_end=985
# @@protoc_insertion_point(module_scope)
