#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
充电桩报文分析脚本

该脚本用于分析指定的充电桩报文数据包并验证其正确性。
"""

import logging
import sys
from datetime import datetime

# 导入消息类
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from messages import BaseMessage, Command, SignInMessage, SignInResponseMessage
from logger import setup_logger

# 设置日志
logger = setup_logger('analyze_message')


def hex_to_bytes(hex_str):
    """
    将十六进制字符串转换为字节数组
    
    Args:
        hex_str: 十六进制字符串
        
    Returns:
        字节数组
    """
    # 移除可能的空格
    hex_str = hex_str.replace(' ', '')
    return bytes.fromhex(hex_str)


def analyze_message(message_bytes):
    """
    分析报文结构
    
    Args:
        message_bytes: 报文字节数组
    """
    if len(message_bytes) < 7:
        logger.error(f"报文长度不足: {len(message_bytes)}")
        return
    
    # 解析报文结构
    start_bytes = message_bytes[0:2]
    length = message_bytes[2]
    version = message_bytes[3]
    sequence_number = message_bytes[4]
    command_code = message_bytes[5]
    data = message_bytes[6:-1]
    checksum = message_bytes[-1]
    
    # 计算校验和
    calculated_checksum = BaseMessage._calculate_checksum(bytes([command_code]) + data)
    
    # 打印报文结构
    logger.info(f"报文结构分析:")
    logger.info(f"  起始域: {start_bytes.hex()}")
    logger.info(f"  长度域: {length}")
    logger.info(f"  版本域: 0x{version:02x}")
    logger.info(f"  序列号域: {sequence_number}")
    logger.info(f"  命令代码: 0x{command_code:02x}")
    logger.info(f"  数据域: {data.hex()}")
    logger.info(f"  校验和域: 0x{checksum:02x} (计算值: 0x{calculated_checksum:02x})")
    
    # 检查校验和
    if checksum != calculated_checksum:
        logger.warning(f"校验和不匹配: 期望 0x{checksum:02x}, 计算 0x{calculated_checksum:02x}")
    
    # 根据命令代码解析报文
    if command_code == Command.CHARGING_PILE_SIGN_IN.value: #0x10
        logger.info("手动解析充电桩签到报文数据域...")
        try:
            index = 0
            
            # 1. 充电桩编码 (8字节)
            charging_pile_code = data[index:index+8]
            index += 8
            logger.info(f"  充电桩编码: {charging_pile_code.hex()}")
            
            # 2. 系统设备资产编码 (20字节)
            system_asset_code = data[index:index+20]
            index += 20
            logger.info(f"  系统设备资产编码: {system_asset_code.hex()}")
            
            # 3. 系统软件版本 (4字节)
            system_version = tuple(data[index:index+4])
            index += 4
            logger.info(f"  系统软件版本: {system_version}")
            
            # 4. 启动次数 (4字节)
            start_count = int.from_bytes(data[index:index+4], byteorder='little')
            index += 4
            logger.info(f"  启动次数: {start_count}")
            
            # 5. 存储空间容量 (4字节，单位：MB)
            storage_capacity = int.from_bytes(data[index:index+4], byteorder='little')
            index += 4
            logger.info(f"  存储空间容量: {storage_capacity} MB")
            
            # 6. 充电桩软件已经持续运行时间 (4字节，单位：分钟)
            running_time = int.from_bytes(data[index:index+4], byteorder='little')
            index += 4
            logger.info(f"  运行时间: {running_time} 分钟")
            
            # 7. 最近一次启动时间 (8字节)
            start_time_data = data[index:index+8]
            start_time = datetime(
                2000 + start_time_data[0],  # 年，从2000年开始计算
                start_time_data[1],         # 月
                start_time_data[2],         # 日
                start_time_data[3],         # 时
                start_time_data[4],         # 分
                start_time_data[5]          # 秒
            )
            index += 8
            logger.info(f"  最近一次启动时间: {start_time}")
            
            # 8. 最近一次签到时间 (8字节)
            sign_in_time_data = data[index:index+8]
            sign_in_time = datetime(
                2000 + sign_in_time_data[0],  # 年，从2000年开始计算
                sign_in_time_data[1],         # 月
                sign_in_time_data[2],         # 日
                sign_in_time_data[3],         # 时
                sign_in_time_data[4],         # 分
                sign_in_time_data[5]          # 秒
            )
            index += 8
            logger.info(f"  最近一次签到时间: {sign_in_time}")
            
            # 生成响应
            response = generate_sign_in_response(charging_pile_code)
            logger.info(f"生成的响应报文: {response.hex()}")
            
        except Exception as e:
            logger.error(f"手动解析签到报文数据域时出错: {e}")
    else:
        logger.warning(f"未支持的命令代码: 0x{command_code:02x}")


def generate_sign_in_response(charging_pile_code):
    """
    生成签到响应报文
    
    Args:
        charging_pile_code: 充电桩编码
        
    Returns:
        响应报文字节数组
    """
    # 创建签到响应报文对象
    response_message = SignInResponseMessage()
    
    # 设置充电桩编码
    response_message.set_charging_pile_code( charging_pile_code)
    
    # 设置电价信息
    price_service_version = 1  # 示例电价服务费版本号
    price_tiers = [
        {'start_time': '00:00', 'end_time': '17:59', 'price': 0},  # 谷时段
        # {'start_time': '08:00', 'end_time': '17:00', 'price': 80},  # 平时段
        # {'start_time': '17:00', 'end_time': '23:59', 'price': 120}   # 峰时段
    ]
    response_message.set_price_service_version(price_service_version)
    response_message.set_price_info(price_tiers)
    
    # 设置服务费信息
    service_fee_tiers = [
        {'start_time': '00:00', 'end_time': '17:59', 'price': 0}  # 全天固定服务费
    ]
    response_message.set_service_fee_info(service_fee_tiers)
    
    # 编码响应报文
    return response_message.encode()


def main():
    """
    主函数
    """
    if len(sys.argv) > 1:
        # 从命令行参数获取测试数据包
        test_data = sys.argv[1]
    else:
        # 默认测试数据包
        # 充电桩编码为3206000970000100
        test_data = "FAF53F80551032060009700001000000000000000000000000000000000000000000082230001D010000800C000003000000E9070303092733FFE907030508192AFF63"
    
    # 转换为字节数组
    message_bytes = hex_to_bytes(test_data)
    
    # 分析报文
    logger.info(f"原始报文: {test_data}")
    analyze_message(message_bytes)


if __name__ == "__main__":
    main()