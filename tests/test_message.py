#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
充电桩报文测试脚本

该脚本用于测试解析充电桩发送的数据包并验证其正确性。
"""

import logging
import sys
from datetime import datetime

# 导入消息类
from messages import BaseMessage, Command, SignInMessage, SignInResponseMessage
from logger import setup_logger

# 设置日志
logger = setup_logger('test_message')


def hex_to_bytes(hex_str):
    """
    将十六进制字符串转换为字节数组
    
    Args:
        hex_str: 十六进制字符串
        
    Returns:
        字节数组
    """
    # 移除可能的空格
    hex_str = hex_str.replace(' ', '')
    return bytes.fromhex(hex_str)


def analyze_message(message_bytes):
    """
    分析报文结构
    
    Args:
        message_bytes: 报文字节数组
    """
    if len(message_bytes) < 7:
        logger.error(f"报文长度不足: {len(message_bytes)}")
        return
    
    # 解析报文结构
    start_bytes = message_bytes[0:2]
    length = message_bytes[2]
    version = message_bytes[3]
    sequence_number = message_bytes[4]
    command_code = message_bytes[5]
    data = message_bytes[6:-1]
    checksum = message_bytes[-1]
    
    # 计算校验和
    calculated_checksum = BaseMessage._calculate_checksum(bytes([command_code]) + data)
    
    # 打印报文结构
    logger.info(f"报文结构分析:")
    logger.info(f"  起始域: {start_bytes.hex()}")
    logger.info(f"  长度域: {length}")
    logger.info(f"  版本域: 0x{version:02x}")
    logger.info(f"  序列号域: {sequence_number}")
    logger.info(f"  命令代码: 0x{command_code:02x}")
    logger.info(f"  数据域: {data.hex()}")
    logger.info(f"  校验和域: 0x{checksum:02x} (计算值: 0x{calculated_checksum:02x})")
    
    # 检查校验和
    if checksum != calculated_checksum:
        logger.warning(f"校验和不匹配: 期望 0x{checksum:02x}, 计算 0x{calculated_checksum:02x}")
    
    # 根据命令代码解析报文
    if command_code == Command.CHARGING_PILE_SIGN_IN.value: # 0x10

        logger.info("解析充电桩签到报文...")
        sign_in_message = SignInMessage.decode(message_bytes)


        if sign_in_message:
            logger.info(f"  用户ID: {sign_in_message.charger_id}")
            logger.info(f"  指令序号: {sign_in_message.command_sequence}")
            logger.info(f"  充电桩编码: {sign_in_message.charging_pile_code.hex()}")
            logger.info(f"  电价服务费版本号: {sign_in_message.price_service_version}")
            logger.info(f"  电价阶梯数: {len(sign_in_message.price_tiers)}")
            for i, tier in enumerate(sign_in_message.price_tiers):
                logger.info(f"    阶梯{i+1}: {tier['start_time']}-{tier['end_time']}, 价格: {tier['price']}分")
            logger.info(f"  服务费阶梯数: {len(sign_in_message.service_fee_tiers)}")
            for i, tier in enumerate(sign_in_message.service_fee_tiers):
                logger.info(f"    阶梯{i+1}: {tier['start_time']}-{tier['end_time']}, 价格: {tier['price']}分")
            
            # 生成响应
            response = generate_sign_in_response(sign_in_message)
            logger.info(f"生成的响应报文: {response.hex()}")
        else:
            logger.error("解析签到报文失败")
    elif command_code == Command.SIGN_IN_RESPONSE.value: #0x09
        logger.info("解析签到响应报文...")
        response_message = SignInResponseMessage.decode(message_bytes)
        if response_message:
            logger.info(f"  充电桩编码: {response_message.charging_pile_code.hex()}")
            logger.info(f"  系统设备资产编码: {response_message.system_asset_code.hex()}")
            logger.info(f"  系统软件版本: {response_message.system_version}")
            logger.info(f"  启动次数: {response_message.start_count}")
            logger.info(f"  存储空间容量: {response_message.storage_capacity} MB")
            logger.info(f"  运行时间: {response_message.running_time} 分钟")
            logger.info(f"  最近一次启动时间: {response_message.start_time}")
            logger.info(f"  最近一次签到时间: {response_message.sign_in_time}")
        else:
            logger.error("解析签到响应报文失败")
    else:
        logger.warning(f"未知的命令代码: 0x{command_code:02x}")


def generate_sign_in_response(sign_in_message):
    """
    生成签到响应报文
    
    Args:
        sign_in_message: 签到报文对象
        
    Returns:
        响应报文字节数组
    """
    # 创建签到响应报文对象
    response_message = SignInResponseMessage()
    
    # 设置充电桩编码
    response_message.set_charging_pile_code(sign_in_message.charging_pile_code)
    
    # 设置系统信息
    system_asset_code = b'SYSTEM_ASSET_CODE_001'.ljust(20, b'\x00')  # 示例系统设备资产编码
    system_version = (1, 0, 0, 0)  # 示例系统软件版本
    start_count = 1000  # 示例启动次数
    storage_capacity = 2048  # 示例存储空间容量，单位：MB
    running_time = 12345  # 示例运行时间，单位：分钟
    
    response_message.set_system_info(
        system_asset_code,
        system_version,
        start_count,
        storage_capacity,
        running_time
    )
    
    # 设置时间信息
    start_time = datetime(2023, 3, 3, 9, 39, 51)  # 示例启动时间
    sign_in_time = datetime.now()  # 当前时间作为签到时间
    
    response_message.set_time_info(start_time, sign_in_time)
    
    # 编码响应报文
    return response_message.encode()


def main():
    """
    主函数
    """
    # 测试数据包
    # 充电桩编码为3206000970000100
    test_data = "FAF53F802D10320600093206000970000100000000000000000000000000000000082230001D010000800C000002000000E9070303092733FFE907030508182AFFA2"
    
    # 转换为字节数组
    message_bytes = hex_to_bytes(test_data)
    
    # 分析报文
    logger.info(f"原始报文: {test_data}")
    analyze_message(message_bytes)


if __name__ == "__main__":
    main()