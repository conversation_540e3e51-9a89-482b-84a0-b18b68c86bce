#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
充电桩客户端/服务器通信测试脚本

该脚本用于测试充电桩客户端与中心系统服务器之间的通信。
模拟客户端发送签到报文并接收服务器响应，验证通信是否正常。
"""

import asyncio
import logging
import sys
from datetime import datetime
from typing import Optional, List, Union
# 导入消息类和日志设置
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from messages import BaseMessage, Command, SignInMessage, SignInResponseMessage, CommunicationMode, CommunicationModeResponse, TimeSync, TimeSyncResponse,HeartBeatResponse,OperationParamsMessage,OperationParamsResponseMessage
from logger import setup_logger

# 设置日志
logger = setup_logger('test_client_server')


class TestClient:
    """
    测试客户端类
    
    实现了一个简单的TCP客户端，用于向服务器发送报文并接收响应。
    """
    
    def __init__(self, client_id: str, host: str = '127.0.0.1', port: int = 8080):
        """
        初始化客户端
        
        Args:
            client_id: 客户端ID，用于标识不同的充电桩
            host: 服务器地址，默认为127.0.0.1
            port: 服务器端口，默认为8080
        """
        self.client_id = client_id
        self.host = host
        self.port = port
        self.reader = None
        self.writer = None
        self.connected = False
    
    async def connect(self) -> bool:
        """
        连接到服务器
        
        Returns:
            连接是否成功
        """
        try:
            logger.info(f"客户端 {self.client_id} 尝试连接到服务器 {self.host}:{self.port}")
            self.reader, self.writer = await asyncio.open_connection(
                self.host, self.port)
            self.connected = True
            logger.info(f"客户端 {self.client_id} 已连接到服务器 {self.host}:{self.port}")
            return True
        except Exception as e:
            logger.error(f"客户端 {self.client_id} 连接服务器失败: {e}")
            self.connected = False
            return False
    
    async def disconnect(self):
        """
        断开与服务器的连接
        """
        if self.writer:
            self.writer.close()
            try:
                await self.writer.wait_closed()
            except:
                pass
            self.writer = None
            self.reader = None
            self.connected = False
            logger.info(f"客户端 {self.client_id} 已断开与服务器的连接")
    
    async def send_message(self, message: bytes) -> List[bytes]:
        """
        发送报文并接收响应
        
        Args:
            message: 要发送的报文
            
        Returns:
            接收到的响应报文
        """
        if not self.connected or not self.writer or not self.reader:
            logger.warning(f"客户端 {self.client_id} 未连接到服务器，无法发送报文")
            return None
        
        try:
            # 发送报文
            self.writer.write(message)
            await self.writer.drain()
            logger.info(f"客户端 {self.client_id} 已发送报文: {message.hex()}")
            
            # 接收响应 - 可能有多个响应
            responses = []
            
            # 尝试读取多个响应（签到响应、通信模式设置、时间同步）
            for _ in range(4):  # 最多尝试读取3个响应
                try:
                    # 读取起始域
                    start_bytes = await asyncio.wait_for(self.reader.readexactly(2), timeout=2.0)
                    if start_bytes != bytes([0xFA, 0xF5]):
                        logger.warning(f"客户端 {self.client_id} 接收到无效的起始域: {start_bytes.hex()}")
                        continue
                    
                    # 读取长度域
                    length_byte = await self.reader.readexactly(1)
                    length = length_byte[0]
                    
                    # 读取剩余的报文
                    remaining_data = await self.reader.readexactly(length+1)
                    
                    # 完整的响应报文
                    response = start_bytes + length_byte + remaining_data
                    logger.info(f"客户端 {self.client_id} 接收到响应: {response.hex()}")
                    
                    responses.append(response)
                except asyncio.TimeoutError:
                    # 超时表示没有更多响应
                    break
                except Exception as e:
                    logger.error(f"读取响应时出错: {e}")
                    break
            
            return responses
            
        except asyncio.IncompleteReadError:
            logger.error(f"客户端 {self.client_id} 读取响应时连接断开")
            self.connected = False
            return None
        except Exception as e:
            logger.error(f"客户端 {self.client_id} 发送报文或接收响应时出错: {e}")
            return None
    
    async def sign_in(self) -> bool:
        """
        发送签到命令并验证响应
        
        Returns:
            签到是否成功
        """
        # 创建签到报文对象
        # sign_in_message = SignInMessage()
        
        # 设置充电桩编码
        # charging_pile_code = f"CP{self.client_id.zfill(6)}".encode().ljust(8, b'\x00')
        # sign_in_message.set_charging_pile_code(charging_pile_code)
        #
        # # 设置系统信息
        # system_asset_code = f"ASSET{self.client_id.zfill(15)}".encode().ljust(20, b'\x00')
        # system_version = (1, 0, 0, 0)
        # start_count = 1
        # storage_capacity = 1024  # MB
        # running_time = 60  # 分钟
        # sign_in_message.set_system_info(system_asset_code, system_version, start_count, storage_capacity, running_time)
        #
        # # 设置时间信息
        # start_time = datetime.now()
        # sign_in_time = datetime.now()
        # sign_in_message.set_time_info(start_time, sign_in_time)
        
        # 编码报文
        #message = sign_in_message.encode()
        message = 'FAF53F802D1032060009700001000000000000000000000000000000000000000000082230001D010000800C000002000000E9070303092733FFE907030508182AFF61'
        
        # 发送报文并接收响应
        responses = await self.send_message(bytes.fromhex(message))
        if not responses:
            logger.error("未收到任何响应")
            return False
        
        # 验证响应数量
        logger.info(f"收到 {len(responses)} 个响应报文")
        if len(responses) < 3:
            logger.warning(f"预期收到3个响应，但实际收到 {len(responses)} 个")
        
        # 解析并验证每个响应
        success = True
        sign_in_response_found = False
        commu_mode_found = False
        time_sync_found = False
        operation_set_found = True
        
        for response in responses:
            # 获取命令代码
            if len(response) > 5:
                command_code = response[5]
                
                # 1. 验证签到响应 (0x09)
                if command_code == Command.SIGN_IN_RESPONSE.value:
                    sign_in_response_found = True
                    response_message = SignInResponseMessage.decode(response)
                    if not response_message:
                        logger.error("解析签到响应失败")
                        success = False
                        continue
                    
                    # 打印电价信息
                    logger.info(f"签到响应成功，电价信息:")
                    for i, tier in enumerate(response_message.price_tiers):
                        logger.info(f"  阶梯{i+1}: {tier['start_time']}-{tier['end_time']}, 价格: {tier['price']}分")
                    
                    logger.info(f"服务费信息:")
                    for i, tier in enumerate(response_message.service_fee_tiers):
                        logger.info(f"  阶梯{i+1}: {tier['start_time']}-{tier['end_time']}, 价格: {tier['price']}分")
                
                # 2. 验证通信模式设置命令 (0xE0)
                elif command_code == Command.COMMUNICATION_MODE_SET.value:
                    commu_mode_found = True
                    commu_mode = CommunicationMode.decode(response)
                    if not commu_mode:
                        logger.error("解析通信模式设置命令失败")
                        success = False
                        continue
                    
                    logger.info(f"收到通信模式设置命令: 模式={commu_mode.communication_mode.hex()}, 上报间隔={commu_mode.interval}秒")
                    
                    # 创建通信模式响应
                    commu_response = CommunicationModeResponse()
                    commu_response.set_response_info(commu_mode.communication_mode, commu_mode.interval)
                    
                    # 发送通信模式响应
                    self.writer.write(commu_response.encode())
                    await self.writer.drain()
                    logger.info(f"已发送通信模式响应")
                
                # 3. 验证时间同步命令 (0x42)
                elif command_code == Command.TIME_SYNC.value:
                    time_sync_found = True
                    time_sync = TimeSync.decode(response)
                    if not time_sync:
                        logger.error("解析时间同步命令失败")
                        success = False
                        continue
                    
                    # 提取时间信息
                    logger.info(f"收到时间同步命令: 年={time_sync.year}, 月={time_sync.month}, 日={time_sync.day}, 时={time_sync.hour}, 分={time_sync.minute}, 秒={time_sync.second}")
                    
                    # 创建时间同步响应
                    time_response = TimeSyncResponse()
                    time_response.user_id = time_sync.user_id
                    time_response.command_sequence = time_sync.command_sequence
                    time_response.year = time_sync.year
                    time_response.month = time_sync.month
                    time_response.day = time_sync.day
                    time_response.hour = time_sync.hour
                    time_response.minute = time_sync.minute
                    time_response.second = time_sync.second
                    
                    # 发送时间同步响应
                    self.writer.write(time_response.encode())
                    await self.writer.drain()
                    logger.info(f"已发送时间同步响应")

                # 4. 验证运营模式信息 (0x0A)
                elif command_code == Command.OPERATION_PARAMS_SET.value:
                    operation_set_found = True
                    operation = OperationParamsMessage.decode(response)
                    if not operation:
                        logger.error("解析运营信息设置命令失败")
                        success = False
                        continue

                    logger.info(
                        f"收到运营信息设置命令: 模式={operation.param_type}, 值={operation .param_content}")

                    # 创建响应
                    operation_response = OperationParamsResponseMessage()
                    # operation_response.set_param_info(operation.user_id, operation.command_sequence)
                    operation_response.set_operation_result_info(operation.param_type,operation .param_content, 0 )

                    # 发送通信模式响应
                    self.writer.write(operation_response.encode())
                    await self.writer.drain()
                    logger.info(f"已发送运营设置响应")
        
        # 验证是否收到所有预期的响应
        if not sign_in_response_found:
            logger.error("未收到签到响应命令(0x09)")
            success = False
        
        if not commu_mode_found:
            logger.error("未收到通信模式设置命令(0xE0)")
            success = False
        
        if not time_sync_found:
            logger.error("未收到时间同步命令(0x42)")
            success = False

        if not operation_set_found:
            logger.error("未收到运营设置响应(0x1A)")
            success = False
        return success

    async def heartbeat(self) -> bool:
        message = 'FAF509800B5800000000010059'

        # 发送报文并接收响应
        responses = await self.send_message(bytes.fromhex(message))
        if not responses:
            logger.error("未收到任何响应")
            return False

        # 验证响应数量
        logger.info(f"收到 {len(responses)} 个响应报文")
        if len(responses) != 1:
            logger.warning(f"预期收到1个响应，但实际收到 {len(responses)} 个")

        # 解析并验证每个响应
        response = responses[0]
        success = False

        if len(response) > 5:
            command_code = response[5]

            if command_code == Command.HEART_BEAT_RESPONSE.value:
                response_message = HeartBeatResponse.decode(response)
                if not response_message:
                    logger.error("解析心跳响应失败")
                else:
                    logger.info(f"收到心跳回复令: {response.hex()}")
                    success = True
        return success




async def run_test():
    """
    运行测试
    """
    # 创建客户端
    client = TestClient("001")
    
    try:
        # 连接服务器
        if not await client.connect():
            logger.error("客户端连接服务器失败")
            return False
        
        # 发送签到命令并验证响应
        if not await client.sign_in():
            logger.error("客户端签到测试失败")
            return False
        if not await client.heartbeat():
            logger.error("客户端心跳测试失败")
            return False
        
        logger.info("测试完成，通信正常")
        return True
    except Exception as e:
        logger.error(f"测试过程中出错: {e}")
        return False
    finally:
        # 断开客户端连接
        await client.disconnect()


def main():
    """
    主函数
    """
    try:
        # 运行测试
        success = asyncio.run(run_test())
        if success:
            logger.info("测试成功完成")
            return 0
        else:
            logger.error("测试失败")
            return 1
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
    except Exception as e:
        logger.error(f"测试运行出错: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())