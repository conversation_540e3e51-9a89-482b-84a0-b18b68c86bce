#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
充电桩客户端模拟器

该模拟器用于模拟充电桩客户端，向中心系统服务器发送报文并接收响应。
"""

import asyncio
import logging
import random
import sys
from datetime import datetime
from typing import Optional, List

from messages import BaseMessage, SignInMessage, SignInResponseMessage, Command
from logger import setup_logger


class ChargingPileClient:
    """
    充电桩客户端模拟器类
    
    模拟充电桩客户端，向中心系统服务器发送报文并接收响应。
    """
    
    def __init__(self, client_id: str, host: str = '127.0.0.1', port: int = 8080):
        """
        初始化客户端
        
        Args:
            client_id: 客户端ID，用于标识不同的充电桩
            host: 服务器地址，默认为127.0.0.1
            port: 服务器端口，默认为8080
        """
        self.client_id = client_id
        self.host = host
        self.port = port
        self.reader: Optional[asyncio.StreamReader] = None
        self.writer: Optional[asyncio.StreamWriter] = None
        # 不再需要Protocol实例，直接使用消息类
        self.logger = setup_logger(f'client_{client_id}')
        self.connected = False
        self.running = False
    
    async def connect(self) -> bool:
        """
        连接到服务器
        
        Returns:
            连接是否成功
        """
        try:
            self.logger.info(f"尝试连接到服务器 {self.host}:{self.port}")
            self.reader, self.writer = await asyncio.open_connection(
                self.host, self.port)
            self.connected = True
            self.logger.info(f"已连接到服务器 {self.host}:{self.port}")
            return True
        except Exception as e:
            self.logger.error(f"连接服务器失败: {e}")
            self.connected = False
            return False
    
    async def disconnect(self):
        """
        断开与服务器的连接
        """
        if self.writer:
            self.writer.close()
            try:
                await self.writer.wait_closed()
            except:
                pass
            self.writer = None
            self.reader = None
            self.connected = False
            self.logger.info("已断开与服务器的连接")
    
    async def reconnect(self) -> bool:
        """
        重新连接到服务器
        
        Returns:
            重连是否成功
        """
        await self.disconnect()
        return await self.connect()
    
    async def send_message(self, message: bytes) -> Optional[bytes]:
        """
        发送报文并接收响应
        
        Args:
            message: 要发送的报文
            
        Returns:
            接收到的响应报文，如果没有响应则返回None
        """
        if not self.connected or not self.writer or not self.reader:
            self.logger.warning("未连接到服务器，无法发送报文")
            return None
        
        try:
            # 发送报文
            self.writer.write(message)
            await self.writer.drain()
            self.logger.info(f"已发送报文: {message.hex()}")
            
            # 接收响应
            # 读取起始域
            start_bytes = await self.reader.readexactly(2)
            if start_bytes != bytes([0xFA, 0xF5]):
                self.logger.warning(f"接收到无效的起始域: {start_bytes.hex()}")
                return None
            
            # 读取长度域
            length_byte = await self.reader.readexactly(1)
            length = length_byte[0]
            
            # 读取剩余的报文
            remaining_data = await self.reader.readexactly(length)
            
            # 完整的响应报文
            response = start_bytes + length_byte + remaining_data
            self.logger.info(f"接收到响应: {response.hex()}")
            
            return response
            
        except asyncio.IncompleteReadError:
            self.logger.error("读取响应时连接断开")
            self.connected = False
            return None
        except Exception as e:
            self.logger.error(f"发送报文或接收响应时出错: {e}")
            return None
    
    async def sign_in(self) -> bool:
        """
        发送签到命令
        
        Returns:
            签到是否成功
        """
        # 创建签到报文对象
        sign_in_message = SignInMessage()
        
        # 设置充电桩基本信息
        user_id = int(self.client_id) if self.client_id.isdigit() else random.randint(1, 65535)
        command_sequence = random.randint(1, 65535)
        charging_pile_code = f"CP{self.client_id.zfill(6)}".encode().ljust(8, b'\x00')
        sign_in_message.set_charging_pile_info(user_id, command_sequence, charging_pile_code)
        
        # 设置电价信息
        price_service_version = 1
        price_tiers = [
            {'start_time': '00:00', 'end_time': '08:00', 'price': 50},  # 0.5元，单位：分
            {'start_time': '08:00', 'end_time': '17:00', 'price': 100},  # 1.0元，单位：分
            {'start_time': '17:00', 'end_time': '23:59', 'price': 70}   # 0.7元，单位：分
        ]
        sign_in_message.set_price_info(price_service_version, price_tiers)
        
        # 设置服务费信息
        service_fee_tiers = [
            {'start_time': '00:00', 'end_time': '23:59', 'price': 10}  # 0.1元，单位：分
        ]
        sign_in_message.set_service_fee_info(service_fee_tiers)
        
        # 编码报文
        message = sign_in_message.encode()
        
        # 发送报文并接收响应
        response = await self.send_message(message)
        if not response:
            return False
        
        # 解析响应
        parsed_response = SignInResponseMessage.decode(response)
        if not parsed_response:
            return False
        
        # 检查响应命令代码
        if parsed_response.COMMAND_CODE != Command.SIGN_IN_RESPONSE.value:
            self.logger.warning(f"签到响应命令代码错误: {parsed_response.COMMAND_CODE}")
            return False
        
        self.logger.info("签到成功")
        return True
    
    async def run(self):
        """
        运行客户端
        """
        self.running = True
        
        while self.running:
            # 如果未连接，尝试连接
            if not self.connected:
                if not await self.connect():
                    # 连接失败，等待一段时间后重试
                    await asyncio.sleep(5)
                    continue
            
            # 发送签到命令
            if not await self.sign_in():
                # 签到失败，重新连接
                await self.reconnect()
                await asyncio.sleep(5)
                continue
            
            # 签到成功，等待一段时间后再次签到
            await asyncio.sleep(60)
    
    async def stop(self):
        """
        停止客户端
        """
        self.running = False
        await self.disconnect()


async def run_multiple_clients(num_clients: int, host: str = '127.0.0.1', port: int = 8080):
    """
    运行多个客户端
    
    Args:
        num_clients: 客户端数量
        host: 服务器地址
        port: 服务器端口
    """
    clients = []
    tasks = []
    
    # 创建客户端
    for i in range(num_clients):
        client = ChargingPileClient(str(i+1), host, port)
        clients.append(client)
        tasks.append(asyncio.create_task(client.run()))
    
    # 等待所有客户端运行
    try:
        await asyncio.gather(*tasks)
    except KeyboardInterrupt:
        # 停止所有客户端
        for client in clients:
            await client.stop()


def main():
    """
    主函数
    """
    # 解析命令行参数
    import argparse
    parser = argparse.ArgumentParser(description='充电桩客户端模拟器')
    parser.add_argument('-n', '--num-clients', type=int, default=1,
                        help='客户端数量，默认为1')
    parser.add_argument('-H', '--host', type=str, default='127.0.0.1',
                        help='服务器地址，默认为127.0.0.1')
    parser.add_argument('-p', '--port', type=int, default=8080,
                        help='服务器端口，默认为8080')
    args = parser.parse_args()
    
    # 运行客户端
    try:
        asyncio.run(run_multiple_clients(args.num_clients, args.host, args.port))
    except KeyboardInterrupt:
        pass
    except Exception as e:
        logging.error(f"客户端运行出错: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())