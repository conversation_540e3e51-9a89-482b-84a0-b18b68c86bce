#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
充电桩中心系统gRPC服务器

该模块实现了与平台端通信的gRPC服务器，处理平台端发送的远程启动请求，
并将请求转发给充电桩。
"""

import asyncio
import grpc
import logging
import time
from concurrent import futures
from datetime import datetime
from typing import Dict, Optional

# 导入生成的gRPC代码
import protos.charger_service_pb2 as charger_pb2
import protos.charger_service_pb2_grpc as charger_pb2_grpc
from messages import OperationParamsMessage

# 导入消息类和日志设置
from messages.user_authorization import UserAuthorization
from logger import setup_logger

# 创建日志记录器
logger = setup_logger('grpc_server')

# 存储充电桩连接信息的字典
charger_connections: Dict[str, asyncio.StreamWriter] = {}


class ChargerServiceServicer(charger_pb2_grpc.ChargerServiceServicer):
    """
    充电桩服务实现类
    
    实现了gRPC服务定义中的方法，处理平台端的请求。
    """
    
    def __init__(self, clients: Dict[str, asyncio.StreamWriter]):
        """
        初始化服务
        
        Args:
            clients: 充电桩连接字典，键为客户端ID，值为StreamWriter对象
        """
        self.clients = clients
        self.logger = logging.getLogger('grpc_server')
    
    async def send_user_authorization(self, charger_id: str, request) -> bool:
        """
        向充电桩发送用户授权信息
        
        Args:
            charger_id: 充电桩ID
            request: 远程启动请求
            
        Returns:
            是否成功发送
        """
        # 查找充电桩连接
        writer = None
        for client_id, client_writer in self.clients.items():
            if charger_id in client_id:  # 简单匹配，实际应用中可能需要更精确的匹配
                writer = client_writer
                break
        
        if not writer:
            self.logger.error(f"找不到充电桩连接: {charger_id}")
            return False
        
        try:
            # 创建用户授权消息
            auth_message = UserAuthorization()
            
            # 设置基本信息
            auth_message.set_basic_info(0, int(time.time()) % 65536)  # 使用当前时间作为指令序号
            
            # 设置认证信息
            process_seq = request.process_seq.encode('ascii') if request.process_seq else b'\x00' * 8
            auth_message.set_auth_info(
                connector_number=request.connector_number,
                card_number=request.card_number,
                process_seq=process_seq,
                customer_id=request.customer_id
            )
            
            # 设置余额信息
            auth_message.set_balance_info(
                fee_balance=request.fee_balance,
                fee_real_balance=request.fee_real_balance,
                service_balance=request.service_balance,
                service_real_balance=request.service_real_balance
            )
            
            # 设置流水号信息
            auth_message.set_serial_info(
                center_serial=request.center_serial,
                boss_serial=request.boss_serial
            )
            
            # 设置当前时间
            now = datetime.now()
            auth_message.set_time_info(
                year=now.year,
                month=now.month,
                day=now.day,
                hour=now.hour,
                minute=now.minute,
                second=now.second
            )
            
            # 设置充电方式
            auth_message.set_charging_mode(
                charging_type=request.charging_type,
                charging_mode=request.charging_mode,
                charging_parameter=request.charging_parameter
            )
            
            # 编码消息
            encoded_message = auth_message.encode()
            
            # 发送消息
            writer.write(encoded_message)
            await writer.drain()
            
            self.logger.info(f"-->向充电桩 {charger_id} 发送用户授权消息: 0x{auth_message.COMMAND_CODE:02X}")
            return True
            
        except Exception as e:
            self.logger.error(f"发送用户授权消息失败: {e}")
            return False
    
    def RemoteStart(self, request, context):
        """
        处理远程启动请求
        
        Args:
            request: 远程启动请求
            context: gRPC上下文
            
        Returns:
            远程启动响应
        """
        self.logger.info(f"<---收到远程启动请求: 充电桩={request.charger_id}, 端口={request.connector_number}, 卡号={request.card_number}")
        
        # 创建响应对象
        response = charger_pb2.RemoteStartResponse()
        response.charger_id = request.charger_id
        
        # 创建事件循环
        loop = asyncio.new_event_loop()
        
        try:
            # 发送用户授权消息
            success = loop.run_until_complete(
                self.send_user_authorization(request.charger_id, request)
            )
            
            if success:
                response.success = True
                response.message = "远程启动命令已发送"
                self.logger.info(f"远程启动成功: {request.charger_id}")
            else:
                response.success = False
                response.message = "发送远程启动命令失败"
                self.logger.error(f"远程启动失败: {request.charger_id}")
                
        except Exception as e:
            response.success = False
            response.message = f"远程启动出错: {str(e)}"
            self.logger.error(f"远程启动出错: {e}")
        finally:
            loop.close()
            
        return response
    
    async def send_qrcode(self, charger_id: str, qr_code: str) -> bool:
        """
        向充电桩发送二维码设置
        
        Args:
            charger_id: 充电桩ID
            qr_code: 二维码内容
            
        Returns:
            是否成功发送
        """
        # 查找充电桩连接
        writer = None
        for client_id, client_writer in self.clients.items():
            if charger_id in client_id:  # 简单匹配，实际应用中可能需要更精确的匹配
                writer = client_writer
                break
        
        if not writer:
            self.logger.error(f"找不到充电桩连接: {charger_id}")
            return False
        
        try:
            # 创建运营参数设置消息
            qrcode_message = OperationParamsMessage()
            
            # 设置二维码信息 (参数类型7表示二维码)
            qrcode_message.set_operation_info(7, qr_code)
            
            # 编码消息
            encoded_message = qrcode_message.encode()
            
            # 发送消息
            writer.write(encoded_message)
            await writer.drain()
            
            self.logger.info(f"-->向充电桩 {charger_id} 发送二维码设置消息: 0x{qrcode_message.COMMAND_CODE:02X}")
            return True
            
        except Exception as e:
            self.logger.error(f"发送二维码设置消息失败: {e}")
            return False
    
    def SetQRCode(self, request, context):
        """
        处理设置二维码请求
        
        Args:
            request: 设置二维码请求
            context: gRPC上下文
            
        Returns:
            设置二维码响应
        """
        self.logger.info(f"<---收到设置二维码请求: 充电桩={request.charger_id}, 二维码={request.qr_code}")
        
        # 创建响应对象
        response = charger_pb2.SetQRCodeResponse()
        response.charger_id = request.charger_id
        
        # 创建事件循环
        loop = asyncio.new_event_loop()
        
        try:
            # 发送二维码设置消息
            success = loop.run_until_complete(
                self.send_qrcode(request.charger_id, request.qr_code)
            )
            
            if success:
                response.success = True
                response.message = "二维码设置命令已发送"
                self.logger.info(f"二维码设置成功: {request.charger_id}")
            else:
                response.success = False
                response.message = "发送二维码设置命令失败"
                self.logger.error(f"二维码设置失败: {request.charger_id}")
                
        except Exception as e:
            response.success = False
            response.message = f"二维码设置出错: {str(e)}"
            self.logger.error(f"二维码设置出错: {e}")
        finally:
            loop.close()
            
        return response
    
    def DeviceSignIn(self, request, context):
        """
        处理设备签到通知
        
        Args:
            request: 设备签到通知请求
            context: gRPC上下文
            
        Returns:
            设备签到通知响应
        """
        self.logger.info(f"--->收到设备签到通知: 充电桩={request.charger_id}, 设备编码={request.device_code}, 状态={request.status}")
        
        # 创建响应对象
        response = charger_pb2.DeviceSignInResponse()
        
        # 这里可以添加设备签到的处理逻辑
        # 例如更新设备状态数据库等
        
        response.success = True
        response.message = "设备签到通知已处理"
        
        return response


class GRPCServer:
    """
    gRPC服务器类
    
    实现了gRPC服务器，处理平台端的请求。
    """
    
    def __init__(self, host: str = '0.0.0.0', port: int = 50051, clients: Dict[str, asyncio.StreamWriter] = None):
        """
        初始化gRPC服务器
        
        Args:
            host: 服务器监听地址
            port: 服务器监听端口
            clients: 充电桩连接字典
        """
        self.host = host
        self.port = port
        self.server = None
        self.clients = clients if clients is not None else {}
        self.logger = logging.getLogger('grpc_server')
        
    def start(self):
        """
        启动gRPC服务器
        """
        # 创建gRPC服务器
        self.server = grpc.server(futures.ThreadPoolExecutor(max_workers=10))
        
        # 注册服务
        charger_pb2_grpc.add_ChargerServiceServicer_to_server(
            ChargerServiceServicer(self.clients), self.server
        )
        
        # 添加监听地址
        address = f"{self.host}:{self.port}"
        self.server.add_insecure_port(address)
        
        # 启动服务器
        self.server.start()
        self.logger.info(f"gRPC服务器启动，监听地址: {address}")
        
    def stop(self):
        """
        停止gRPC服务器
        """
        if self.server:
            self.server.stop(0)
            self.logger.info("gRPC服务器已停止")


# 如果直接运行此模块，则启动gRPC服务器
if __name__ == "__main__":
    server = GRPCServer()
    server.start()
    
    try:
        # 保持服务器运行
        while True:
            time.sleep(86400)  # 睡眠一天
    except KeyboardInterrupt:
        server.stop()