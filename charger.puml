@startuml
title 国标设备通信时序图
participant "平台端" as platform
participant "中心系统" as centralSystem
participant "充电桩" as chargePoint

chargePoint -> centralSystem: 0x10(签到)
centralSystem -> chargePoint: 0x09(回复签到&计费方案设置)

centralSystem -> platform: grpc接口通知平台设备已连接

centralSystem -> chargePoint: 0xE0(通信模式设置)
chargePoint -> centralSystem: 0xF0(设置应答)

centralSystem -> chargePoint: 0x42(对时)
chargePoint -> centralSystem: 0x52(对时响应)

chargePoint -> centralSystem: 0x58(心跳)
centralSystem -> chargePoint: 0x48(心跳响应)

platform -> centralSystem: grpc接口设置二维码
centralSystem -> chargePoint: 0x0A(二维码)
chargePoint -> centralSystem: 0x1A(二维码响应)

platform -> centralSystem: grpc接口设置远程启动
centralSystem -> chargePoint: 0x6C(合法用户认证通过信息)
chargePoint -> centralSystem: 0x7A(应答中心合法用户认证)

chargePoint -> centralSystem: 0x71(充电订单创建)
centralSystem -> chargePoint: 0x61(充电订单创建响应)
centralSystem -> platform: grpc接口创建充电订单


platform -> centralSystem: grpc接口设置远程停止
centralSystem -> chargePoint: 0x05(远程控制设置)
chargePoint -> centralSystem: 0x15(远程控制响应)

chargePoint -> centralSystem: 0x79(充电订单结算)
chargePoint -> centralSystem: 0x68(充电订单结算响应)
centralSystem -> platform: grpc接口结算充电订单

chargePoint -> centralSystem: 0x34(模块状态)
centralSystem -> platform: grpc接口通知平台

chargePoint -> centralSystem: 0x39(充电详情)
centralSystem -> platform: grpc接口通知平台

@enduml