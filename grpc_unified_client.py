#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
充电桩中心系统gRPC客户端统一工具

该模块提供了充电桩中心系统的gRPC客户端功能，包括：
1. 远程启动充电桩
2. 设置充电桩二维码

使用方法：
- 远程启动: python3 grpc_unified_client.py remote-start --charger-id <ID> --card-number <卡号> [选项]
- 设置二维码: python3 grpc_unified_client.py set-qrcode --charger-id <ID> --qrcode <二维码内容> [选项]
"""

import grpc
import argparse
import sys

# 导入生成的gRPC代码
import protos.charger_service_pb2 as charger_pb2
import protos.charger_service_pb2_grpc as charger_pb2_grpc


def remote_start(server_address, charger_id, connector_number, card_number):
    """
    发送远程启动请求
    
    Args:
        server_address: 服务器地址，格式为 host:port
        charger_id: 充电桩ID
        connector_number: 充电端口号
        card_number: 充电卡号
    """
    # 创建gRPC通道
    with grpc.insecure_channel(server_address) as channel:
        # 创建Stub
        stub = charger_pb2_grpc.ChargerServiceStub(channel)
        
        # 创建请求
        request = charger_pb2.RemoteStartRequest(
            charger_id=charger_id,
            connector_number=connector_number,
            card_number=card_number,
            process_seq="12345678",  # 示例流程序号
            customer_id="VIN12345,粤B12345,DEMO",  # 示例客户号
            fee_balance=100.0,  # 示例电费余额
            fee_real_balance=100.0,  # 示例电费可用余额
            service_balance=50.0,  # 示例服务费余额
            service_real_balance=50.0,  # 示例服务费可用余额
            center_serial="CS123456789",  # 示例中心交易流水
            boss_serial="BS987654321",  # 示例BOSS流水号
            charging_type=1,  # 示例充电方式
            charging_mode=2,  # 示例充电模式 (自然充满)
            charging_parameter=0  # 示例充电参数
        )
        
        # 发送请求
        print(f"发送远程启动请求: 充电桩={charger_id}, 端口={connector_number}, 卡号={card_number}")
        try:
            response = stub.RemoteStart(request)
            
            # 处理响应
            if response.success:
                print(f"远程启动成功: {response.message}")
            else:
                print(f"远程启动失败: {response.message}")
        except grpc.RpcError as e:
            print(f"RPC错误: {e.code()}: {e.details()}")


def set_qrcode(server_address, charger_id, qr_code):
    """
    发送设置二维码请求
    
    Args:
        server_address: 服务器地址，格式为 host:port
        charger_id: 充电桩ID
        qr_code: 二维码内容
    """
    # 创建gRPC通道
    with grpc.insecure_channel(server_address) as channel:
        # 创建Stub
        stub = charger_pb2_grpc.ChargerServiceStub(channel)
        
        # 创建请求
        request = charger_pb2.SetQRCodeRequest(
            charger_id=charger_id,
            qr_code=qr_code
        )
        
        # 发送请求
        try:
            response = stub.SetQRCode(request)
            print(f"设置二维码响应: 成功={response.success}, 消息={response.message}")
        except grpc.RpcError as e:
            print(f"RPC错误: {e.code()}: {e.details()}")


def parse_args():
    """
    解析命令行参数
    
    Returns:
        解析后的参数对象
    """
    # 创建主解析器
    parser = argparse.ArgumentParser(
        description='充电桩中心系统gRPC客户端统一工具',
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    # 添加服务器地址参数（所有子命令共用）
    parser.add_argument('-s', '--server', type=str, default='localhost:50051',
                        help='服务器地址 (默认: localhost:50051)')
    
    # 创建子命令解析器
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    subparsers.required = True
    
    # 远程启动子命令
    remote_start_parser = subparsers.add_parser('remote-start', help='远程启动充电桩')
    remote_start_parser.add_argument('-c', '--charger-id', type=str, required=True,
                                    help='充电桩ID')
    remote_start_parser.add_argument('-p', '--port', type=int, default=1,
                                    help='充电端口号 (默认: 1)')
    remote_start_parser.add_argument('-n', '--card-number', type=str, required=True,
                                    help='充电卡号')
    
    # 设置二维码子命令
    qrcode_parser = subparsers.add_parser('set-qrcode', help='设置充电桩二维码')
    qrcode_parser.add_argument('-c', '--charger-id', type=str, required=True,
                              help='充电桩ID')
    qrcode_parser.add_argument('-q', '--qrcode', type=str, required=True,
                              help='二维码内容')
    
    return parser.parse_args()


def main():
    """
    主函数
    """
    # 解析命令行参数
    args = parse_args()
    
    try:
        # 根据子命令执行相应功能
        if args.command == 'remote-start':
            remote_start(
                server_address=args.server,
                charger_id=args.charger_id,
                connector_number=args.port,
                card_number=args.card_number
            )
        elif args.command == 'set-qrcode':
            set_qrcode(
                server_address=args.server,
                charger_id=args.charger_id,
                qr_code=args.qrcode
            )
        return 0
    except Exception as e:
        print(f"错误: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())