#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
日志模块

该模块实现了按天记录日志的功能，用于记录充电桩与中心系统之间的通信报文。
"""

import logging
import os
import sys
from datetime import datetime
from logging.handlers import TimedRotatingFileHandler

# 存储已创建的日志记录器，实现单例模式
_loggers = {}

def setup_logger(name: str, log_dir: str = 'logs') -> logging.Logger:
    """
    设置日志记录器
    
    Args:
        name: 日志记录器名称
        log_dir: 日志目录，默认为'logs'
        
    Returns:
        配置好的日志记录器
    """
    # 如果已经创建过该名称的日志记录器，直接返回
    if name in _loggers:
        return _loggers[name]
    
    # 创建日志目录
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 创建日志记录器
    logger = logging.getLogger(name)
    logger.setLevel(logging.DEBUG)

    # 禁用日志传播，防止日志被重复处理
    logger.propagate = False
    
    # 检查是否已经添加了处理器，避免重复添加
    if not logger.handlers:
        # 创建按天轮转的文件处理器
        log_file = os.path.join(log_dir, f'{name}.log')
        file_handler = TimedRotatingFileHandler(
            log_file,
            when='midnight',  # 每天午夜轮转
            interval=1,       # 轮转间隔为1天
            backupCount=30,   # 保留30天的日志
            encoding='utf-8'  # 使用UTF-8编码
        )
        file_handler.suffix = '%Y-%m-%d'  # 日志文件后缀格式
        
        # 创建控制台处理器
        # console_handler = logging.StreamHandler(sys.stdout)
        # console_handler.setLevel(logging.INFO)
        
        # 创建错误日志处理器（输出到stderr）
        error_handler = logging.StreamHandler(sys.stderr)
        error_handler.setLevel(logging.ERROR)
        
        # 设置日志格式
        formatter = logging.Formatter(
            '%(asctime)s - [%(module)s:%(lineno)d] - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(formatter)
        #console_handler.setFormatter(formatter)
        error_handler.setFormatter(formatter)

        # 添加处理器到日志记录器
        logger.addHandler(file_handler)
        #logger.addHandler(console_handler)
        logger.addHandler(error_handler)

    
    # 将日志记录器保存到字典中
    _loggers[name] = logger
    
    return logger