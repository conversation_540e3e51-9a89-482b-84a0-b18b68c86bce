2025-03-19 11:03:56 - client_handler - INFO - 新客户端连接: 127.0.0.1:51154
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 4641
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 4635
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3346
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3830
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3244
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3130
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3332
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3036
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3030
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3039
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3730
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3030
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3031
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3030
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3030
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3030
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3030
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3030
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3030
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3030
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3030
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3030
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3030
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3030
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3030
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3030
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3030
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3030
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3030
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3030
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3030
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3030
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3030
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3030
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3038
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3232
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3330
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3030
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3144
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3031
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3030
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3030
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3830
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3043
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3030
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3030
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3032
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3030
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3030
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3030
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 4539
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3037
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3033
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3033
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3039
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3237
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3333
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 4646
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 4539
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3037
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3033
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3035
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3038
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3138
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3241
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 4646
2025-03-19 11:03:56 - client_handler - WARNING - 客户端 127.0.0.1:51154 发送了无效的起始域: 3631
2025-03-19 11:03:58 - client_handler - INFO - 客户端 127.0.0.1:51154 断开连接
2025-03-19 11:07:17 - client_handler - INFO - 新客户端连接: 127.0.0.1:52277
2025-03-19 11:07:17 - client_handler - INFO - 从客户端 127.0.0.1:52277 接收到报文: faf53f802d1032060009700001000000000000000000000000000000000000000000082230001d010000800c000002000000e9070303092733ffe907030508182aff61
2025-03-19 11:07:17 - client_handler - ERROR - 处理客户端 127.0.0.1:52277 报文时出错: Can't instantiate abstract class TimeSync with abstract method decode
2025-03-19 11:07:19 - client_handler - INFO - 客户端 127.0.0.1:52277 断开连接
2025-03-19 11:10:08 - client_handler - INFO - 新客户端连接: 127.0.0.1:52786
2025-03-19 11:10:08 - client_handler - INFO - 从客户端 127.0.0.1:52786 接收到报文: faf53f802d1032060009700001000000000000000000000000000000000000000000082230001d010000800c000002000000e9070303092733ffe907030508182aff61
2025-03-19 11:10:08 - client_handler - INFO - 向客户端 127.0.0.1:52786 发送响应: faf52180080900000000320600097000010001000000010000113b3200010000113b0a0092
2025-03-19 11:10:08 - client_handler - INFO - 向客户端 127.0.0.1:52786 发送响应: faf5098008e000000000010ff0
2025-03-19 11:10:08 - client_handler - INFO - 向客户端 127.0.0.1:52786 发送响应: faf50f80084200000000e90703130b0a08ff64
2025-03-19 11:10:08 - client_handler - INFO - 从客户端 127.0.0.1:52786 接收到报文: faf5098008f000000000010f00
2025-03-19 11:10:08 - client_handler - INFO - 客户端 127.0.0.1:52786 断开连接
2025-03-19 13:44:28 - client_handler - INFO - 新客户端连接: 127.0.0.1:58993
2025-03-19 13:44:28 - client_handler - INFO - 从客户端 127.0.0.1:58993 接收到报文: faf53f802d1032060009700001000000000000000000000000000000000000000000082230001d010000800c000002000000e9070303092733ffe907030508182aff61
2025-03-19 13:44:28 - client_handler - INFO - 向客户端 127.0.0.1:58993 发送响应: faf521801c0900000000320600097000010001000000010000113b3200010000113b0a0092
2025-03-19 13:44:28 - client_handler - INFO - 向客户端 127.0.0.1:58993 发送响应: faf509801ce000000000010ff0
2025-03-19 13:44:28 - client_handler - INFO - 向客户端 127.0.0.1:58993 发送响应: faf50f801c4200000000e90703130d2c1cff9c
2025-03-19 13:44:28 - client_handler - INFO - 从客户端 127.0.0.1:58993 接收到报文: faf509801cf000000000010f00
2025-03-19 13:44:28 - client_handler - INFO - 客户端 127.0.0.1:58993 断开连接
2025-03-19 13:48:33 - client_handler - INFO - 新客户端连接: 127.0.0.1:59158
2025-03-19 13:48:33 - client_handler - INFO - 从客户端 127.0.0.1:59158 接收到报文: faf53f802d1032060009700001000000000000000000000000000000000000000000082230001d010000800c000002000000e9070303092733ffe907030508182aff61
2025-03-19 13:48:33 - client_handler - INFO - 向客户端 127.0.0.1:59158 发送响应: faf52180210900000000320600097000010001000000010000113b3200010000113b0a0092
2025-03-19 13:48:33 - client_handler - INFO - 向客户端 127.0.0.1:59158 发送响应: faf5098021e000000000010ff0
2025-03-19 13:48:33 - client_handler - INFO - 向客户端 127.0.0.1:59158 发送响应: faf50f80214200000000e90703130d3021ffa5
2025-03-19 13:48:33 - client_handler - INFO - 从客户端 127.0.0.1:59158 接收到报文: faf5098021f000000000010f00
2025-03-19 13:48:33 - client_handler - INFO - 客户端 127.0.0.1:59158 断开连接
2025-03-19 13:52:38 - client_handler - INFO - 新客户端连接: 127.0.0.1:59486
2025-03-19 13:52:38 - client_handler - INFO - 从客户端 127.0.0.1:59486 接收到报文: faf53f802d1032060009700001000000000000000000000000000000000000000000082230001d010000800c000002000000e9070303092733ffe907030508182aff61
2025-03-19 13:52:38 - client_handler - INFO - 向客户端 127.0.0.1:59486 发送响应: faf52180260900000000320600097000010001000000010000113b3200010000113b0a0092
2025-03-19 13:52:38 - client_handler - INFO - 向客户端 127.0.0.1:59486 发送响应: faf5098026e000000000010ff0
2025-03-19 13:52:38 - client_handler - INFO - 向客户端 127.0.0.1:59486 发送响应: faf50f80264200000000e90703130d3426ffae
2025-03-19 13:52:38 - client_handler - INFO - 从客户端 127.0.0.1:59486 接收到报文: faf5098026f000000000010f00
2025-03-19 13:52:38 - client_handler - INFO - 从客户端 127.0.0.1:59486 接收到报文: faf50f80265200000000e90703130d3426ffbe
2025-03-19 13:52:38 - client_handler - INFO - 客户端 127.0.0.1:59486 断开连接
2025-03-19 14:20:08 - client_handler - INFO - 新客户端连接: 127.0.0.1:60765
2025-03-19 14:20:08 - client_handler - INFO - 从客户端 127.0.0.1:60765 接收到报文: faf53f802d1032060009700001000000000000000000000000000000000000000000082230001d010000800c000002000000e9070303092733ffe907030508182aff61
2025-03-19 14:20:08 - client_handler - ERROR - 处理客户端 127.0.0.1:60765 报文时出错: 'int' object has no attribute 'hex'
2025-03-19 14:20:10 - client_handler - INFO - 客户端 127.0.0.1:60765 断开连接
2025-03-19 14:49:53 - client_handler - INFO - 新客户端连接: 127.0.0.1:62359
2025-03-19 14:49:53 - client_handler - INFO - 从客户端 127.0.0.1:62359 接收到报文: faf53f802d1032060009700001000000000000000000000000000000000000000000082230001d010000800c000002000000e9070303092733ffe907030508182aff61
2025-03-19 14:49:53 - client_handler - INFO - 向客户端 127.0.0.1:62359 发送响应: faf52180350900000000320600097000010001000000010000113b3200010000113b0a0092
2025-03-19 14:49:53 - client_handler - INFO - 向客户端 127.0.0.1:62359 发送响应: faf5098035e000000000010ff0
2025-03-19 14:49:53 - client_handler - INFO - 向客户端 127.0.0.1:62359 发送响应: faf50f80354200000000e90703130e3135ffbb
2025-03-19 14:49:53 - client_handler - INFO - 从客户端 127.0.0.1:62359 接收到报文: faf5098035f000000000010f00
2025-03-19 14:49:53 - client_handler - INFO - 从客户端 127.0.0.1:62359 接收到报文: faf50f80355200000000e90703130e3135ffcb
2025-03-19 14:49:53 - client_handler - INFO - 客户端 127.0.0.1:62359 断开连接
2025-03-19 15:13:23 - client_handler - INFO - 新客户端连接: 127.0.0.1:63817
2025-03-19 15:13:23 - client_handler - INFO - 从客户端 127.0.0.1:63817 接收到报文: faf53f802d1032060009700001000000000000000000000000000000000000000000082230001d010000800c000002000000e9070303092733ffe907030508182aff61
2025-03-19 15:13:23 - client_handler - INFO - 向客户端 127.0.0.1:63817 发送响应: faf52180170900000000320600097000010001000000010000113b3200010000113b0a0092
2025-03-19 15:13:23 - client_handler - INFO - 向客户端 127.0.0.1:63817 发送响应: faf5098017e000000000010ff0
2025-03-19 15:13:23 - client_handler - INFO - 向客户端 127.0.0.1:63817 发送响应: faf50f80174200000000e90703130f0d17ff7a
2025-03-19 15:13:23 - client_handler - INFO - 从客户端 127.0.0.1:63817 接收到报文: faf5098017f000000000010f00
2025-03-19 15:13:23 - client_handler - INFO - 从客户端 127.0.0.1:63817 接收到报文: faf50f80175200000000e90703130f0d17ff8a
2025-03-19 15:13:23 - client_handler - INFO - 客户端 127.0.0.1:63817 断开连接
2025-03-19 15:14:49 - client_handler - INFO - 新客户端连接: 127.0.0.1:63876
2025-03-19 15:14:49 - client_handler - INFO - 从客户端 127.0.0.1:63876 接收到报文: faf53f802d1032060009700001000000000000000000000000000000000000000000082230001d010000800c000002000000e9070303092733ffe907030508182aff61
2025-03-19 15:14:49 - client_handler - INFO - 向客户端 127.0.0.1:63876 发送响应: faf52180310900000000320600097000010001000000010000113b3200010000113b0a0092
2025-03-19 15:14:49 - client_handler - INFO - 向客户端 127.0.0.1:63876 发送响应: faf5098031e000000000010ff0
2025-03-19 15:14:49 - client_handler - INFO - 向客户端 127.0.0.1:63876 发送响应: faf50f80314200000000e90703130f0e31ff95
2025-03-19 15:14:49 - client_handler - INFO - 从客户端 127.0.0.1:63876 接收到报文: faf5098031f000000000010f00
2025-03-19 15:14:49 - client_handler - INFO - 从客户端 127.0.0.1:63876 接收到报文: faf50f80315200000000e90703130f0e31ffa5
2025-03-19 15:14:49 - client_handler - INFO - 客户端 127.0.0.1:63876 断开连接
2025-03-19 15:20:40 - client_handler - INFO - 新客户端连接: 127.0.0.1:64096
2025-03-19 15:20:40 - client_handler - INFO - 从客户端 127.0.0.1:64096 接收到报文: faf53f802d1032060009700001000000000000000000000000000000000000000000082230001d010000800c000002000000e9070303092733ffe907030508182aff61
2025-03-19 15:20:40 - client_handler - INFO - 向客户端 127.0.0.1:64096 发送响应: faf52180280900000000320600097000010001000000010000113b3200010000113b0a0092
2025-03-19 15:20:40 - client_handler - INFO - 向客户端 127.0.0.1:64096 发送响应: faf5098028e000000000010ff0
2025-03-19 15:20:40 - client_handler - INFO - 向客户端 127.0.0.1:64096 发送响应: faf50f80284200000000e90703130f1428ff92
2025-03-19 15:20:40 - client_handler - INFO - 从客户端 127.0.0.1:64096 接收到报文: faf5098028f000000000010f00
2025-03-19 15:20:40 - client_handler - INFO - 从客户端 127.0.0.1:64096 接收到报文: faf50f80285200000000e90703130f1428ffa2
2025-03-19 15:20:40 - client_handler - INFO - 客户端 127.0.0.1:64096 断开连接
2025-03-19 15:24:09 - client_handler - INFO - 新客户端连接: 127.0.0.1:64342
2025-03-19 15:24:09 - client_handler - INFO - 从客户端 127.0.0.1:64342 接收到报文: faf53f802d1032060009700001000000000000000000000000000000000000000000082230001d010000800c000002000000e9070303092733ffe907030508182aff61
2025-03-19 15:24:09 - client_handler - INFO - 向客户端 127.0.0.1:64342 发送响应: faf52180090900000000320600097000010001000000010000113b3200010000113b0a0092
2025-03-19 15:24:09 - client_handler - INFO - 向客户端 127.0.0.1:64342 发送响应: faf5098009e000000000010ff0
2025-03-19 15:24:09 - client_handler - INFO - 向客户端 127.0.0.1:64342 发送响应: faf50f80094200000000e90703130f1809ff77
2025-03-19 15:24:09 - client_handler - INFO - 从客户端 127.0.0.1:64342 接收到报文: faf5098009f000000000010f00
2025-03-19 15:24:09 - client_handler - INFO - 从客户端 127.0.0.1:64342 接收到报文: faf50f80095200000000e90703130f1809ff87
2025-03-19 15:24:09 - client_handler - INFO - 客户端 127.0.0.1:64342 断开连接
2025-03-19 15:30:11 - client_handler - INFO - 新客户端连接: 127.0.0.1:64549
2025-03-19 15:30:11 - client_handler - INFO - 从客户端 127.0.0.1:64549 接收到报文: faf53f802d1032060009700001000000000000000000000000000000000000000000082230001d010000800c000002000000e9070303092733ffe907030508182aff61
2025-03-19 15:30:11 - client_handler - INFO - 向客户端 127.0.0.1:64549 发送响应: faf521800b0900000000320600097000010001000000010000113b3200010000113b0a0092
2025-03-19 15:30:11 - client_handler - INFO - 向客户端 127.0.0.1:64549 发送响应: faf509800be000000000010ff0
2025-03-19 15:30:11 - client_handler - INFO - 向客户端 127.0.0.1:64549 发送响应: faf50f800b4200000000e90703130f1e0bff7f
2025-03-19 15:30:11 - client_handler - INFO - 从客户端 127.0.0.1:64549 接收到报文: faf509800bf000000000010f00
2025-03-19 15:30:11 - client_handler - INFO - 从客户端 127.0.0.1:64549 接收到报文: faf50f800b5200000000e90703130f1e0bff8f
2025-03-19 15:30:11 - client_handler - INFO - 客户端 127.0.0.1:64549 断开连接
2025-03-19 15:36:31 - client_handler - INFO - 新客户端连接: 127.0.0.1:64776
2025-03-19 15:36:31 - client_handler - INFO - 从客户端 127.0.0.1:64776 接收到报文: faf53f802d1032060009700001000000000000000000000000000000000000000000082230001d010000800c000002000000e9070303092733ffe907030508182aff61
2025-03-19 15:36:31 - client_handler - INFO - 向客户端 127.0.0.1:64776 发送响应: faf521801f0900000000320600097000010001000000010000113b3200010000113b0a0092
2025-03-19 15:36:31 - client_handler - INFO - 向客户端 127.0.0.1:64776 发送响应: faf509801fe000000000010ff0
2025-03-19 15:36:31 - client_handler - INFO - 向客户端 127.0.0.1:64776 发送响应: faf50f801f4200000000e90703130f241fff99
2025-03-19 15:36:31 - client_handler - INFO - 从客户端 127.0.0.1:64776 接收到报文: faf509801ff000000000010f00
2025-03-19 15:36:31 - client_handler - INFO - 从客户端 127.0.0.1:64776 接收到报文: faf50f801f5200000000e90703130f241fffa9
2025-03-19 15:36:31 - client_handler - ERROR - 处理客户端 127.0.0.1:64776 报文时出错: 'int' object has no attribute 'hex'
2025-03-19 15:36:31 - client_handler - INFO - 客户端 127.0.0.1:64776 断开连接
2025-03-19 15:38:48 - client_handler - INFO - 新客户端连接: 127.0.0.1:65003
2025-03-19 15:38:48 - client_handler - INFO - 从客户端 127.0.0.1:65003 接收到报文: faf53f802d1032060009700001000000000000000000000000000000000000000000082230001d010000800c000002000000e9070303092733ffe907030508182aff61
2025-03-19 15:38:48 - client_handler - INFO - 向客户端 127.0.0.1:65003 发送响应: faf52180300900000000320600097000010001000000010000113b3200010000113b0a0092
2025-03-19 15:38:48 - client_handler - INFO - 向客户端 127.0.0.1:65003 发送响应: faf5098030e000000000010ff0
2025-03-19 15:38:48 - client_handler - INFO - 向客户端 127.0.0.1:65003 发送响应: faf50f80304200000000e90703130f2630ffac
2025-03-19 15:38:48 - client_handler - INFO - 从客户端 127.0.0.1:65003 接收到报文: faf5098030f000000000010f00
2025-03-19 15:38:48 - client_handler - INFO - 从客户端 127.0.0.1:65003 接收到报文: faf50f80305200000000e90703130f2630ffbc
2025-03-19 15:38:48 - client_handler - INFO - 客户端 127.0.0.1:65003 断开连接
2025-03-19 17:45:53 - client_handler - INFO - 新客户端连接: 127.0.0.1:55004
2025-03-19 17:45:53 - client_handler - INFO - 从客户端 127.0.0.1:55004 接收到报文: faf53f802d1032060009700001000000000000000000000000000000000000000000082230001d010000800c000002000000e9070303092733ffe907030508182aff61
2025-03-19 17:45:53 - client_handler - INFO - 向客户端 127.0.0.1:55004 发送响应: faf52180350900000000320600097000010001000000010000113b3200010000113b0a0092
2025-03-19 17:45:53 - client_handler - INFO - 向客户端 127.0.0.1:55004 发送响应: faf5098035e000000000010ff0
2025-03-19 17:45:53 - client_handler - INFO - 向客户端 127.0.0.1:55004 发送响应: faf50f80354200000000e9070313112d35ffba
2025-03-19 17:45:53 - client_handler - INFO - 从客户端 127.0.0.1:55004 接收到报文: faf5098035f000000000010f00
2025-03-19 17:45:53 - client_handler - INFO - 从客户端 127.0.0.1:55004 接收到报文: faf50f80355200000000e9070313112d35ffca
2025-03-19 17:45:53 - client_handler - INFO - 从客户端 127.0.0.1:55004 接收到报文: faf50980344800000000010049
2025-03-19 17:45:55 - client_handler - INFO - 客户端 127.0.0.1:55004 断开连接
2025-03-19 17:52:41 - client_handler - INFO - 新客户端连接: 127.0.0.1:55489
2025-03-19 17:52:41 - client_handler - INFO - 从客户端 127.0.0.1:55489 接收到报文: faf53f802d1032060009700001000000000000000000000000000000000000000000082230001d010000800c000002000000e9070303092733ffe907030508182aff61
2025-03-19 17:52:41 - client_handler - INFO - 向客户端 127.0.0.1:55489 发送响应: faf52180290900000000320600097000010001000000010000113b3200010000113b0a0092
2025-03-19 17:52:41 - client_handler - INFO - 向客户端 127.0.0.1:55489 发送响应: faf5098029e000000000010ff0
2025-03-19 17:52:41 - client_handler - INFO - 向客户端 127.0.0.1:55489 发送响应: faf50f80294200000000e9070313113429ffb5
2025-03-19 17:52:41 - client_handler - INFO - 从客户端 127.0.0.1:55489 接收到报文: faf5098029f000000000010f00
2025-03-19 17:52:41 - client_handler - INFO - 从客户端 127.0.0.1:55489 接收到报文: faf50f80295200000000e9070313113429ffc5
2025-03-19 17:52:41 - client_handler - INFO - 从客户端 127.0.0.1:55489 接收到报文: faf50980344800000000010049
2025-03-19 17:52:43 - client_handler - INFO - 客户端 127.0.0.1:55489 断开连接
2025-03-19 23:01:55 - client_handler - INFO - 新客户端连接: 127.0.0.1:51520
2025-03-19 23:01:55 - client_handler - INFO - 从客户端 127.0.0.1:51520 接收到报文: faf53f802d1032060009700001000000000000000000000000000000000000000000082230001d010000800c000002000000e9070303092733ffe907030508182aff61
2025-03-19 23:01:55 - client_handler - INFO - 向客户端 127.0.0.1:51520 发送响应: faf52180370900000000320600097000010001000000010000113b3200010000113b0a0092
2025-03-19 23:01:55 - client_handler - INFO - 向客户端 127.0.0.1:51520 发送响应: faf5098037e000000000010ff0
2025-03-19 23:01:55 - client_handler - INFO - 向客户端 127.0.0.1:51520 发送响应: faf50f80374200000000e9070313170137ff96
2025-03-19 23:01:55 - client_handler - INFO - 从客户端 127.0.0.1:51520 接收到报文: faf5098037f000000000010f00
2025-03-19 23:01:55 - client_handler - INFO - 从客户端 127.0.0.1:51520 接收到报文: faf50f80375200000000e9070313170137ffa6
2025-03-19 23:01:55 - client_handler - INFO - 从客户端 127.0.0.1:51520 接收到报文: faf50980344800000000010049
2025-03-19 23:01:57 - client_handler - INFO - 客户端 127.0.0.1:51520 断开连接
2025-03-19 23:06:31 - client_handler - INFO - 新客户端连接: 127.0.0.1:51563
2025-03-19 23:06:31 - client_handler - INFO - 从客户端 127.0.0.1:51563 接收到报文: faf53f802d1032060009700001000000000000000000000000000000000000000000082230001d010000800c000002000000e9070303092733ffe907030508182aff61
2025-03-19 23:06:31 - client_handler - INFO - 向客户端 127.0.0.1:51563 发送响应: faf521801f0900000000320600097000010001000000010000113b3200010000113b0a0092
2025-03-19 23:06:31 - client_handler - INFO - 向客户端 127.0.0.1:51563 发送响应: faf509801fe000000000010ff0
2025-03-19 23:06:31 - client_handler - INFO - 向客户端 127.0.0.1:51563 发送响应: faf50f801f4200000000e907031317061fff83
2025-03-19 23:06:31 - client_handler - INFO - 从客户端 127.0.0.1:51563 接收到报文: faf509801ff000000000010f00
2025-03-19 23:06:31 - client_handler - INFO - 从客户端 127.0.0.1:51563 接收到报文: faf50f801f5200000000e907031317061fff93
2025-03-19 23:06:31 - client_handler - INFO - 从客户端 127.0.0.1:51563 接收到报文: faf50980075800000000000058
2025-03-19 23:06:31 - client_handler - INFO - 向客户端 127.0.0.1:51563 发送响应: faf509801f4800000000000048
2025-03-19 23:06:33 - client_handler - INFO - 客户端 127.0.0.1:51563 断开连接
2025-03-19 23:07:08 - client_handler - INFO - 新客户端连接: 127.0.0.1:51567
2025-03-19 23:07:08 - client_handler - INFO - 从客户端 127.0.0.1:51567 接收到报文: faf53f802d1032060009700001000000000000000000000000000000000000000000082230001d010000800c000002000000e9070303092733ffe907030508182aff61
2025-03-19 23:07:08 - client_handler - INFO - 向客户端 127.0.0.1:51567 发送响应: faf52180080900000000320600097000010001000000010000113b3200010000113b0a0092
2025-03-19 23:07:08 - client_handler - INFO - 向客户端 127.0.0.1:51567 发送响应: faf5098008e000000000010ff0
2025-03-19 23:07:08 - client_handler - INFO - 向客户端 127.0.0.1:51567 发送响应: faf50f80084200000000e9070313170708ff6d
2025-03-19 23:07:08 - client_handler - INFO - 从客户端 127.0.0.1:51567 接收到报文: faf5098008f000000000010f00
2025-03-19 23:07:08 - client_handler - INFO - 从客户端 127.0.0.1:51567 接收到报文: faf50f80085200000000e9070313170708ff7d
2025-03-19 23:07:08 - client_handler - INFO - 从客户端 127.0.0.1:51567 接收到报文: faf509800b5800000000010059
2025-03-19 23:07:08 - client_handler - INFO - 向客户端 127.0.0.1:51567 发送响应: faf50980084800000000010049
2025-03-19 23:07:10 - client_handler - INFO - 客户端 127.0.0.1:51567 断开连接
