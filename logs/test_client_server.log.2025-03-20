2025-03-20 00:50:52 - [test_client_server:60] - INFO - 客户端 001 尝试连接到服务器 127.0.0.1:8080
2025-03-20 00:50:52 - [test_client_server:64] - INFO - 客户端 001 已连接到服务器 127.0.0.1:8080
2025-03-20 00:50:52 - [test_client_server:104] - INFO - 客户端 001 已发送报文: 4641463533463830324431303332303630303039373030303031303030303030303030303030303030303030303030303030303030303030303030303030303030303030303832323330303031443031303030303830304330303030303230303030303045393037303330333039323733334646453930373033303530383138324146463631
2025-03-20 00:50:54 - [test_client_server:181] - ERROR - 未收到任何响应
2025-03-20 00:50:54 - [test_client_server:299] - ERROR - 客户端签到测试失败
2025-03-20 00:50:54 - [test_client_server:84] - INFO - 客户端 001 已断开与服务器的连接
2025-03-20 00:50:54 - [test_client_server:323] - ERROR - 测试失败
2025-03-20 00:53:13 - [test_client_server:60] - INFO - 客户端 001 尝试连接到服务器 127.0.0.1:8080
2025-03-20 00:53:13 - [test_client_server:64] - INFO - 客户端 001 已连接到服务器 127.0.0.1:8080
2025-03-20 00:53:13 - [test_client_server:106] - INFO - 客户端 001 已发送报文: 4641463533463830324431303332303630303039373030303031303030303030303030303030303030303030303030303030303030303030303030303030303030303030303832323330303031443031303030303830304330303030303230303030303045393037303330333039323733334646453930373033303530383138324146463631
2025-03-20 00:53:15 - [test_client_server:183] - ERROR - 未收到任何响应
2025-03-20 00:53:15 - [test_client_server:301] - ERROR - 客户端签到测试失败
2025-03-20 00:53:15 - [test_client_server:84] - INFO - 客户端 001 已断开与服务器的连接
2025-03-20 00:53:15 - [test_client_server:325] - ERROR - 测试失败
2025-03-20 00:57:23 - [test_client_server:60] - INFO - 客户端 001 尝试连接到服务器 127.0.0.1:8080
2025-03-20 00:57:23 - [test_client_server:64] - INFO - 客户端 001 已连接到服务器 127.0.0.1:8080
2025-03-20 00:57:23 - [test_client_server:106] - INFO - 客户端 001 已发送报文: faf53f802d1032060009700001000000000000000000000000000000000000000000082230001d010000800c000002000000e9070303092733ffe907030508182aff61
2025-03-20 00:57:23 - [test_client_server:129] - INFO - 客户端 001 接收到响应: faf52180170900000000320600097000010001000000010000113b3200010000113b0a0092
2025-03-20 00:57:23 - [test_client_server:129] - INFO - 客户端 001 接收到响应: faf5098017e000000000010ff0
2025-03-20 00:57:23 - [test_client_server:129] - INFO - 客户端 001 接收到响应: faf50f80174200000000e9070314003917ff98
2025-03-20 00:57:23 - [test_client_server:187] - INFO - 收到 3 个响应报文
2025-03-20 00:57:23 - [test_client_server:212] - INFO - 签到响应成功，电价信息:
2025-03-20 00:57:23 - [test_client_server:214] - INFO -   阶梯1: 00:00-17:59, 价格: 50分
2025-03-20 00:57:23 - [test_client_server:216] - INFO - 服务费信息:
2025-03-20 00:57:23 - [test_client_server:218] - INFO -   阶梯1: 00:00-17:59, 价格: 10分
2025-03-20 00:57:23 - [test_client_server:229] - INFO - 收到通信模式设置命令: 模式=01, 上报间隔=15秒
2025-03-20 00:57:23 - [test_client_server:240] - INFO - 已发送通信模式响应
2025-03-20 00:57:23 - [test_client_server:252] - INFO - 收到时间同步命令: 年=2025, 月=3, 日=20, 时=0, 分=57, 秒=23
2025-03-20 00:57:23 - [test_client_server:307] - ERROR - 测试过程中出错: 'bytes' object has no attribute 'to_bytes'
2025-03-20 00:57:23 - [test_client_server:84] - INFO - 客户端 001 已断开与服务器的连接
2025-03-20 00:57:23 - [test_client_server:325] - ERROR - 测试失败
2025-03-20 01:04:42 - [test_client_server:60] - INFO - 客户端 001 尝试连接到服务器 127.0.0.1:8080
2025-03-20 01:04:42 - [test_client_server:64] - INFO - 客户端 001 已连接到服务器 127.0.0.1:8080
2025-03-20 01:04:42 - [test_client_server:106] - INFO - 客户端 001 已发送报文: faf53f802d1032060009700001000000000000000000000000000000000000000000082230001d010000800c000002000000e9070303092733ffe907030508182aff61
2025-03-20 01:04:42 - [test_client_server:129] - INFO - 客户端 001 接收到响应: faf521802a0900000000320600097000010001000000010000113b3200010000113b0a0092
2025-03-20 01:04:42 - [test_client_server:129] - INFO - 客户端 001 接收到响应: faf509802ae000000000010ff0
2025-03-20 01:04:42 - [test_client_server:129] - INFO - 客户端 001 接收到响应: faf50f802a4200000000e907031401042aff77
2025-03-20 01:04:42 - [test_client_server:187] - INFO - 收到 3 个响应报文
2025-03-20 01:04:42 - [test_client_server:212] - INFO - 签到响应成功，电价信息:
2025-03-20 01:04:42 - [test_client_server:214] - INFO -   阶梯1: 00:00-17:59, 价格: 50分
2025-03-20 01:04:42 - [test_client_server:216] - INFO - 服务费信息:
2025-03-20 01:04:42 - [test_client_server:218] - INFO -   阶梯1: 00:00-17:59, 价格: 10分
2025-03-20 01:04:42 - [test_client_server:229] - INFO - 收到通信模式设置命令: 模式=01, 上报间隔=15秒
2025-03-20 01:04:42 - [test_client_server:240] - INFO - 已发送通信模式响应
2025-03-20 01:04:42 - [test_client_server:252] - INFO - 收到时间同步命令: 年=2025, 月=3, 日=20, 时=1, 分=4, 秒=42
2025-03-20 01:04:42 - [test_client_server:268] - INFO - 已发送时间同步响应
2025-03-20 01:04:42 - [test_client_server:304] - INFO - 测试完成，通信正常
2025-03-20 01:04:42 - [test_client_server:84] - INFO - 客户端 001 已断开与服务器的连接
2025-03-20 01:04:42 - [test_client_server:322] - INFO - 测试成功完成
2025-03-20 01:14:38 - [test_client_server:60] - INFO - 客户端 001 尝试连接到服务器 127.0.0.1:8080
2025-03-20 01:14:38 - [test_client_server:64] - INFO - 客户端 001 已连接到服务器 127.0.0.1:8080
2025-03-20 01:14:38 - [test_client_server:106] - INFO - 客户端 001 已发送报文: faf53f802d1032060009700001000000000000000000000000000000000000000000082230001d010000800c000002000000e9070303092733ffe907030508182aff61
2025-03-20 01:14:38 - [test_client_server:129] - INFO - 客户端 001 接收到响应: faf52180260900000000320600097000010001000000010000113b3200010000113b0a0092
2025-03-20 01:14:38 - [test_client_server:129] - INFO - 客户端 001 接收到响应: faf5098026e000000000010ff0
2025-03-20 01:14:38 - [test_client_server:129] - INFO - 客户端 001 接收到响应: faf50f80264200000000e9070314010e26ff7d
2025-03-20 01:14:38 - [test_client_server:187] - INFO - 收到 3 个响应报文
2025-03-20 01:14:38 - [test_client_server:212] - INFO - 签到响应成功，电价信息:
2025-03-20 01:14:38 - [test_client_server:214] - INFO -   阶梯1: 00:00-17:59, 价格: 50分
2025-03-20 01:14:38 - [test_client_server:216] - INFO - 服务费信息:
2025-03-20 01:14:38 - [test_client_server:218] - INFO -   阶梯1: 00:00-17:59, 价格: 10分
2025-03-20 01:14:38 - [test_client_server:229] - INFO - 收到通信模式设置命令: 模式=01, 上报间隔=15秒
2025-03-20 01:14:38 - [test_client_server:240] - INFO - 已发送通信模式响应
2025-03-20 01:14:38 - [test_client_server:252] - INFO - 收到时间同步命令: 年=2025, 月=3, 日=20, 时=1, 分=14, 秒=38
2025-03-20 01:14:38 - [test_client_server:268] - INFO - 已发送时间同步响应
2025-03-20 01:14:38 - [test_client_server:106] - INFO - 客户端 001 已发送报文: faf509800b5800000000010059
2025-03-20 01:14:38 - [test_client_server:129] - INFO - 客户端 001 接收到响应: faf50980264800000000010049
2025-03-20 01:14:40 - [test_client_server:295] - INFO - 收到 1 个响应报文
2025-03-20 01:14:40 - [test_client_server:311] - INFO - 收到心跳回复令: faf50980264800000000010049
2025-03-20 01:14:40 - [test_client_server:339] - INFO - 测试完成，通信正常
2025-03-20 01:14:40 - [test_client_server:84] - INFO - 客户端 001 已断开与服务器的连接
2025-03-20 01:14:40 - [test_client_server:357] - INFO - 测试成功完成
2025-03-20 01:21:34 - [test_client_server:60] - INFO - 客户端 001 尝试连接到服务器 127.0.0.1:8080
2025-03-20 01:21:34 - [test_client_server:64] - INFO - 客户端 001 已连接到服务器 127.0.0.1:8080
2025-03-20 01:21:34 - [test_client_server:104] - INFO - 客户端 001 已发送报文: faf53f802d1032060009700001000000000000000000000000000000000000000000082230001d010000800c000002000000e9070303092733ffe907030508182aff61
2025-03-20 01:21:34 - [test_client_server:127] - INFO - 客户端 001 接收到响应: faf52180220900000000320600097000010001000000010000113b3200010000113b0a0092
2025-03-20 01:21:34 - [test_client_server:127] - INFO - 客户端 001 接收到响应: faf5098022e000000000010ff0
2025-03-20 01:21:34 - [test_client_server:127] - INFO - 客户端 001 接收到响应: faf50f80224200000000e9070314011522ff80
2025-03-20 01:21:34 - [test_client_server:185] - INFO - 收到 3 个响应报文
2025-03-20 01:21:34 - [test_client_server:210] - INFO - 签到响应成功，电价信息:
2025-03-20 01:21:34 - [test_client_server:212] - INFO -   阶梯1: 00:00-17:59, 价格: 50分
2025-03-20 01:21:34 - [test_client_server:214] - INFO - 服务费信息:
2025-03-20 01:21:34 - [test_client_server:216] - INFO -   阶梯1: 00:00-17:59, 价格: 10分
2025-03-20 01:21:34 - [test_client_server:227] - INFO - 收到通信模式设置命令: 模式=01, 上报间隔=15秒
2025-03-20 01:21:34 - [test_client_server:238] - INFO - 已发送通信模式响应
2025-03-20 01:21:34 - [test_client_server:250] - INFO - 收到时间同步命令: 年=2025, 月=3, 日=20, 时=1, 分=21, 秒=34
2025-03-20 01:21:34 - [test_client_server:266] - INFO - 已发送时间同步响应
2025-03-20 01:21:34 - [test_client_server:104] - INFO - 客户端 001 已发送报文: faf509800b5800000000010059
2025-03-20 01:21:34 - [test_client_server:127] - INFO - 客户端 001 接收到响应: faf50980224800000000010049
2025-03-20 01:21:36 - [test_client_server:293] - INFO - 收到 1 个响应报文
2025-03-20 01:21:36 - [test_client_server:309] - INFO - 收到心跳回复令: faf50980224800000000010049
2025-03-20 01:21:36 - [test_client_server:337] - INFO - 测试完成，通信正常
2025-03-20 01:21:36 - [test_client_server:84] - INFO - 客户端 001 已断开与服务器的连接
2025-03-20 01:21:36 - [test_client_server:355] - INFO - 测试成功完成
2025-03-20 01:24:08 - [test_client_server:60] - INFO - 客户端 001 尝试连接到服务器 127.0.0.1:8080
2025-03-20 01:24:08 - [test_client_server:64] - INFO - 客户端 001 已连接到服务器 127.0.0.1:8080
2025-03-20 01:24:08 - [test_client_server:104] - INFO - 客户端 001 已发送报文: faf53f802d1032060009700001000000000000000000000000000000000000000000082230001d010000800c000002000000e9070303092733ffe907030508182aff61
2025-03-20 01:24:08 - [test_client_server:127] - INFO - 客户端 001 接收到响应: faf52180080900000000320600097000010001000000010000113b3200010000113b0a0092
2025-03-20 01:24:08 - [test_client_server:127] - INFO - 客户端 001 接收到响应: faf5098008e000000000010ff0
2025-03-20 01:24:08 - [test_client_server:127] - INFO - 客户端 001 接收到响应: faf50f80084200000000e9070314011808ff69
2025-03-20 01:24:08 - [test_client_server:185] - INFO - 收到 3 个响应报文
2025-03-20 01:24:08 - [test_client_server:210] - INFO - 签到响应成功，电价信息:
2025-03-20 01:24:08 - [test_client_server:212] - INFO -   阶梯1: 00:00-17:59, 价格: 50分
2025-03-20 01:24:08 - [test_client_server:214] - INFO - 服务费信息:
2025-03-20 01:24:08 - [test_client_server:216] - INFO -   阶梯1: 00:00-17:59, 价格: 10分
2025-03-20 01:24:08 - [test_client_server:227] - INFO - 收到通信模式设置命令: 模式=01, 上报间隔=15秒
2025-03-20 01:24:08 - [test_client_server:238] - INFO - 已发送通信模式响应
2025-03-20 01:24:08 - [test_client_server:250] - INFO - 收到时间同步命令: 年=2025, 月=3, 日=20, 时=1, 分=24, 秒=8
2025-03-20 01:24:08 - [test_client_server:266] - INFO - 已发送时间同步响应
2025-03-20 01:24:08 - [test_client_server:104] - INFO - 客户端 001 已发送报文: faf509800b5800000000010059
2025-03-20 01:24:08 - [test_client_server:127] - INFO - 客户端 001 接收到响应: faf50980084800000000010049
2025-03-20 01:24:10 - [test_client_server:293] - INFO - 收到 1 个响应报文
2025-03-20 01:24:10 - [test_client_server:309] - INFO - 收到心跳回复令: faf50980084800000000010049
2025-03-20 01:24:10 - [test_client_server:337] - INFO - 测试完成，通信正常
2025-03-20 01:24:10 - [test_client_server:84] - INFO - 客户端 001 已断开与服务器的连接
2025-03-20 01:24:10 - [test_client_server:355] - INFO - 测试成功完成
2025-03-20 01:26:26 - [test_client_server:60] - INFO - 客户端 001 尝试连接到服务器 127.0.0.1:8080
2025-03-20 01:26:26 - [test_client_server:64] - INFO - 客户端 001 已连接到服务器 127.0.0.1:8080
2025-03-20 01:26:26 - [test_client_server:104] - INFO - 客户端 001 已发送报文: faf53f802d1032060009700001000000000000000000000000000000000000000000082230001d010000800c000002000000e9070303092733ffe907030508182aff61
2025-03-20 01:26:26 - [test_client_server:127] - INFO - 客户端 001 接收到响应: faf521801a0900000000320600097000010001000000010000113b3200010000113b0a0092
2025-03-20 01:26:26 - [test_client_server:127] - INFO - 客户端 001 接收到响应: faf509801ae000000000010ff0
2025-03-20 01:26:26 - [test_client_server:127] - INFO - 客户端 001 接收到响应: faf50f801a4200000000e9070314011a1aff7d
2025-03-20 01:26:26 - [test_client_server:185] - INFO - 收到 3 个响应报文
2025-03-20 01:26:26 - [test_client_server:210] - INFO - 签到响应成功，电价信息:
2025-03-20 01:26:26 - [test_client_server:212] - INFO -   阶梯1: 00:00-17:59, 价格: 50分
2025-03-20 01:26:26 - [test_client_server:214] - INFO - 服务费信息:
2025-03-20 01:26:26 - [test_client_server:216] - INFO -   阶梯1: 00:00-17:59, 价格: 10分
2025-03-20 01:26:26 - [test_client_server:227] - INFO - 收到通信模式设置命令: 模式=01, 上报间隔=15秒
2025-03-20 01:26:26 - [test_client_server:238] - INFO - 已发送通信模式响应
2025-03-20 01:26:26 - [test_client_server:250] - INFO - 收到时间同步命令: 年=2025, 月=3, 日=20, 时=1, 分=26, 秒=26
2025-03-20 01:26:26 - [test_client_server:266] - INFO - 已发送时间同步响应
2025-03-20 01:26:26 - [test_client_server:104] - INFO - 客户端 001 已发送报文: faf509800b5800000000010059
2025-03-20 01:26:26 - [test_client_server:127] - INFO - 客户端 001 接收到响应: faf509801a4800000000010049
2025-03-20 01:26:28 - [test_client_server:293] - INFO - 收到 1 个响应报文
2025-03-20 01:26:28 - [test_client_server:309] - INFO - 收到心跳回复令: faf509801a4800000000010049
2025-03-20 01:26:28 - [test_client_server:337] - INFO - 测试完成，通信正常
2025-03-20 01:26:28 - [test_client_server:84] - INFO - 客户端 001 已断开与服务器的连接
2025-03-20 01:26:28 - [test_client_server:355] - INFO - 测试成功完成
2025-03-20 01:37:18 - [test_client_server:60] - INFO - 客户端 001 尝试连接到服务器 127.0.0.1:8080
2025-03-20 01:37:18 - [test_client_server:64] - INFO - 客户端 001 已连接到服务器 127.0.0.1:8080
2025-03-20 01:37:18 - [test_client_server:104] - INFO - 客户端 001 已发送报文: faf53f802d1032060009700001000000000000000000000000000000000000000000082230001d010000800c000002000000e9070303092733ffe907030508182aff61
2025-03-20 01:37:18 - [test_client_server:127] - INFO - 客户端 001 接收到响应: faf52180120900000000320600097000010001000000010000113b3200010000113b0a0092
2025-03-20 01:37:18 - [test_client_server:127] - INFO - 客户端 001 接收到响应: faf5098012e000000000010ff0
2025-03-20 01:37:18 - [test_client_server:127] - INFO - 客户端 001 接收到响应: faf50f80124200000000e9070314012512ff80
2025-03-20 01:37:18 - [test_client_server:185] - INFO - 收到 3 个响应报文
2025-03-20 01:37:18 - [test_client_server:210] - INFO - 签到响应成功，电价信息:
2025-03-20 01:37:18 - [test_client_server:212] - INFO -   阶梯1: 00:00-17:59, 价格: 50分
2025-03-20 01:37:18 - [test_client_server:214] - INFO - 服务费信息:
2025-03-20 01:37:18 - [test_client_server:216] - INFO -   阶梯1: 00:00-17:59, 价格: 10分
2025-03-20 01:37:18 - [test_client_server:227] - INFO - 收到通信模式设置命令: 模式=01, 上报间隔=15秒
2025-03-20 01:37:18 - [test_client_server:238] - INFO - 已发送通信模式响应
2025-03-20 01:37:18 - [test_client_server:250] - INFO - 收到时间同步命令: 年=2025, 月=3, 日=20, 时=1, 分=37, 秒=18
2025-03-20 01:37:18 - [test_client_server:266] - INFO - 已发送时间同步响应
2025-03-20 01:37:18 - [test_client_server:104] - INFO - 客户端 001 已发送报文: faf509800b5800000000010059
2025-03-20 01:37:18 - [test_client_server:127] - INFO - 客户端 001 接收到响应: faf50980124800000000010049
2025-03-20 01:37:20 - [test_client_server:293] - INFO - 收到 1 个响应报文
2025-03-20 01:37:20 - [test_client_server:309] - INFO - 收到心跳回复令: faf50980124800000000010049
2025-03-20 01:37:20 - [test_client_server:337] - INFO - 测试完成，通信正常
2025-03-20 01:37:20 - [test_client_server:84] - INFO - 客户端 001 已断开与服务器的连接
2025-03-20 01:37:20 - [test_client_server:355] - INFO - 测试成功完成
2025-03-20 15:43:58 - [test_client_server:60] - INFO - 客户端 001 尝试连接到服务器 127.0.0.1:8080
2025-03-20 15:43:58 - [test_client_server:64] - INFO - 客户端 001 已连接到服务器 127.0.0.1:8080
2025-03-20 15:43:58 - [test_client_server:104] - INFO - 客户端 001 已发送报文: faf53f802d1032060009700001000000000000000000000000000000000000000000082230001d010000800c000002000000e9070303092733ffe907030508182aff61
2025-03-20 15:43:58 - [test_client_server:127] - INFO - 客户端 001 接收到响应: faf521803a0900000000320600097000010001000000010000113b3200010000113b0a0092
2025-03-20 15:43:58 - [test_client_server:127] - INFO - 客户端 001 接收到响应: faf509803ae000000000010ff0
2025-03-20 15:43:58 - [test_client_server:127] - INFO - 客户端 001 接收到响应: faf50f803a4200000000e90703140f2b3affbc
2025-03-20 15:43:58 - [test_client_server:127] - INFO - 客户端 001 接收到响应: faf56c803a0a0000000001b0b2bbd5d7bfd4bdb5e7c6f8d3d0cfdeb9abcbbe0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000bb
2025-03-20 15:43:58 - [test_client_server:185] - INFO - 收到 4 个响应报文
2025-03-20 15:43:58 - [test_client_server:211] - INFO - 签到响应成功，电价信息:
2025-03-20 15:43:58 - [test_client_server:213] - INFO -   阶梯1: 00:00-17:59, 价格: 50分
2025-03-20 15:43:58 - [test_client_server:215] - INFO - 服务费信息:
2025-03-20 15:43:58 - [test_client_server:217] - INFO -   阶梯1: 00:00-17:59, 价格: 10分
2025-03-20 15:43:58 - [test_client_server:228] - INFO - 收到通信模式设置命令: 模式=01, 上报间隔=15秒
2025-03-20 15:43:58 - [test_client_server:239] - INFO - 已发送通信模式响应
2025-03-20 15:43:58 - [test_client_server:251] - INFO - 收到时间同步命令: 年=2025, 月=3, 日=20, 时=15, 分=43, 秒=58
2025-03-20 15:43:58 - [test_client_server:267] - INFO - 已发送时间同步响应
2025-03-20 15:43:58 - [test_client_server:278] - INFO - 收到运营信息设置命令: 模式=1, 值=安徽卓越电气有限公司
2025-03-20 15:43:58 - [test_client_server:289] - INFO - 已发送运营设置响应
2025-03-20 15:43:58 - [test_client_server:104] - INFO - 客户端 001 已发送报文: faf509800b5800000000010059
2025-03-20 15:43:58 - [test_client_server:127] - INFO - 客户端 001 接收到响应: faf509803a4800000000010049
2025-03-20 15:44:00 - [test_client_server:319] - INFO - 收到 1 个响应报文
2025-03-20 15:44:00 - [test_client_server:335] - INFO - 收到心跳回复令: faf509803a4800000000010049
2025-03-20 15:44:00 - [test_client_server:363] - INFO - 测试完成，通信正常
2025-03-20 15:44:00 - [test_client_server:84] - INFO - 客户端 001 已断开与服务器的连接
2025-03-20 15:44:00 - [test_client_server:381] - INFO - 测试成功完成
