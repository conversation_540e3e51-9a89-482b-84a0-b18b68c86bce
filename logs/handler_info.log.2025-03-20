2025-03-20 00:50:52 - [client_handler:38] - INFO - 新客户端连接: 127.0.0.1:52248
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 4641
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 4635
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3346
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3830
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3244
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3130
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3332
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3036
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3030
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3039
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3730
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3030
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3031
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3030
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3030
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3030
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3030
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3030
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3030
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3030
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3030
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3030
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3030
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3030
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3030
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3030
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3030
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3030
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3030
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3030
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3030
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3030
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3030
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3030
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3038
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3232
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3330
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3030
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3144
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3031
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3030
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3030
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3830
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3043
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3030
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3030
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3032
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3030
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3030
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3030
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 4539
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3037
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3033
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3033
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3039
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3237
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3333
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 4646
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 4539
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3037
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3033
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3035
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3038
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3138
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3241
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 4646
2025-03-20 00:50:52 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52248 发送了无效的起始域: 3631
2025-03-20 00:50:54 - [client_handler:93] - INFO - 客户端 127.0.0.1:52248 断开连接
2025-03-20 00:53:13 - [client_handler:38] - INFO - 新客户端连接: 127.0.0.1:52288
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 4641
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 4635
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3346
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3830
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3244
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3130
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3332
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3036
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3030
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3039
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3730
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3030
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3031
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3030
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3030
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3030
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3030
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3030
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3030
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3030
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3030
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3030
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3030
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3030
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3030
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3030
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3030
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3030
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3030
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3030
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3030
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3030
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3030
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3030
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3038
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3232
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3330
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3030
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3144
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3031
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3030
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3030
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3830
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3043
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3030
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3030
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3032
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3030
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3030
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3030
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 4539
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3037
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3033
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3033
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3039
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3237
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3333
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 4646
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 4539
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3037
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3033
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3035
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3038
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3138
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3241
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 4646
2025-03-20 00:53:13 - [client_handler:45] - WARNING - 客户端 127.0.0.1:52288 发送了无效的起始域: 3631
2025-03-20 00:53:15 - [client_handler:93] - INFO - 客户端 127.0.0.1:52288 断开连接
2025-03-20 00:57:23 - [client_handler:38] - INFO - 新客户端连接: 127.0.0.1:52997
2025-03-20 00:57:23 - [client_handler:59] - INFO - 从客户端 127.0.0.1:52997 接收到报文: faf53f802d1032060009700001000000000000000000000000000000000000000000082230001d010000800c000002000000e9070303092733ffe907030508182aff61
2025-03-20 00:57:23 - [command_handler:44] - INFO - 收到命令: 充电桩签到命令，客户端ID: 127.0.0.1:52997，命令代码: 0x10
2025-03-20 00:57:23 - [command_handler:56] - INFO - <-- 收到充电桩: 3206000970000100，命令代码: 0x10，充电桩签到命令
2025-03-20 00:57:23 - [command_handler:82] - INFO - -> 向充电桩 3206000970000100 发送： 0x09
2025-03-20 00:57:23 - [command_handler:89] - INFO - --> 向充电桩 3206000970000100 发送： 0xE0 ,通信模式设置命令 b'\x01'
2025-03-20 00:57:23 - [command_handler:97] - INFO - --> 向充电桩 3206000970000100 发送：0x42, 发送时间2025-03-20 00:57:23 同步命令
2025-03-20 00:57:23 - [client_handler:82] - INFO - 向客户端 127.0.0.1:52997 发送响应: faf52180170900000000320600097000010001000000010000113b3200010000113b0a0092
2025-03-20 00:57:23 - [client_handler:82] - INFO - 向客户端 127.0.0.1:52997 发送响应: faf5098017e000000000010ff0
2025-03-20 00:57:23 - [client_handler:82] - INFO - 向客户端 127.0.0.1:52997 发送响应: faf50f80174200000000e9070314003917ff98
2025-03-20 00:57:23 - [client_handler:59] - INFO - 从客户端 127.0.0.1:52997 接收到报文: faf5098017f000000000010f00
2025-03-20 00:57:23 - [command_handler:44] - INFO - 收到命令: 通信模式响应命令，客户端ID: 127.0.0.1:52997，命令代码: 0xF0
2025-03-20 00:57:23 - [command_handler:104] - INFO - <- 设备127.0.0.1:52997,回复0x F0，通信模式响应命令，设置: 01, 上报间隔: 15秒
2025-03-20 00:57:23 - [client_handler:93] - INFO - 客户端 127.0.0.1:52997 断开连接
2025-03-20 01:04:42 - [client_handler:38] - INFO - 新客户端连接: 127.0.0.1:53206
2025-03-20 01:04:42 - [client_handler:59] - INFO - 从客户端 127.0.0.1:53206 接收到报文: faf53f802d1032060009700001000000000000000000000000000000000000000000082230001d010000800c000002000000e9070303092733ffe907030508182aff61
2025-03-20 01:04:42 - [command_handler:44] - INFO - 收到命令: 充电桩签到命令，客户端ID: 127.0.0.1:53206，命令代码: 0x10
2025-03-20 01:04:42 - [command_handler:56] - INFO - <-- 收到充电桩: 3206000970000100，命令代码: 0x10，充电桩签到命令
2025-03-20 01:04:42 - [command_handler:82] - INFO - -> 向充电桩 3206000970000100 发送： 0x09
2025-03-20 01:04:42 - [command_handler:89] - INFO - --> 向充电桩 3206000970000100 发送： 0xE0 ,通信模式设置命令 b'\x01'
2025-03-20 01:04:42 - [command_handler:97] - INFO - --> 向充电桩 3206000970000100 发送：0x42, 发送时间2025-03-20 01:04:42 同步命令
2025-03-20 01:04:42 - [client_handler:82] - INFO - 向客户端 127.0.0.1:53206 发送响应: faf521802a0900000000320600097000010001000000010000113b3200010000113b0a0092
2025-03-20 01:04:42 - [client_handler:82] - INFO - 向客户端 127.0.0.1:53206 发送响应: faf509802ae000000000010ff0
2025-03-20 01:04:42 - [client_handler:82] - INFO - 向客户端 127.0.0.1:53206 发送响应: faf50f802a4200000000e907031401042aff77
2025-03-20 01:04:42 - [client_handler:59] - INFO - 从客户端 127.0.0.1:53206 接收到报文: faf509802af000000000010f00
2025-03-20 01:04:42 - [command_handler:44] - INFO - 收到命令: 通信模式响应命令，客户端ID: 127.0.0.1:53206，命令代码: 0xF0
2025-03-20 01:04:42 - [command_handler:104] - INFO - <- 设备127.0.0.1:53206,回复0x F0，通信模式响应命令，设置: 01, 上报间隔: 15秒
2025-03-20 01:04:42 - [client_handler:59] - INFO - 从客户端 127.0.0.1:53206 接收到报文: faf50f802a5200000000e907031401042aff87
2025-03-20 01:04:42 - [command_handler:44] - INFO - 收到命令: 充电桩时间同步响应，客户端ID: 127.0.0.1:53206，命令代码: 0x52
2025-03-20 01:04:42 - [command_handler:110] - INFO - <- 设备127.0.0.1:53206,回复0x52，充电桩时间同步响应#，设置时间: 2025-03-20 01:04:42
2025-03-20 01:04:42 - [client_handler:93] - INFO - 客户端 127.0.0.1:53206 断开连接
2025-03-20 01:14:38 - [client_handler:38] - INFO - 新客户端连接: 127.0.0.1:53706
2025-03-20 01:14:38 - [client_handler:59] - INFO - 从客户端 127.0.0.1:53706 接收到报文: faf53f802d1032060009700001000000000000000000000000000000000000000000082230001d010000800c000002000000e9070303092733ffe907030508182aff61
2025-03-20 01:14:38 - [command_handler:44] - INFO - 收到命令: 充电桩签到命令，客户端ID: 127.0.0.1:53706，命令代码: 0x10
2025-03-20 01:14:38 - [command_handler:56] - INFO - <-- 收到充电桩: 3206000970000100，命令代码: 0x10，充电桩签到命令
2025-03-20 01:14:38 - [command_handler:82] - INFO - -> 向充电桩 3206000970000100 发送： 0x09
2025-03-20 01:14:38 - [command_handler:89] - INFO - --> 向充电桩 3206000970000100 发送： 0xE0 ,通信模式设置命令 b'\x01'
2025-03-20 01:14:38 - [command_handler:97] - INFO - --> 向充电桩 3206000970000100 发送：0x42, 发送时间2025-03-20 01:14:38 同步命令
2025-03-20 01:14:38 - [client_handler:82] - INFO - 向客户端 127.0.0.1:53706 发送响应: faf52180260900000000320600097000010001000000010000113b3200010000113b0a0092
2025-03-20 01:14:38 - [client_handler:82] - INFO - 向客户端 127.0.0.1:53706 发送响应: faf5098026e000000000010ff0
2025-03-20 01:14:38 - [client_handler:82] - INFO - 向客户端 127.0.0.1:53706 发送响应: faf50f80264200000000e9070314010e26ff7d
2025-03-20 01:14:38 - [client_handler:59] - INFO - 从客户端 127.0.0.1:53706 接收到报文: faf5098026f000000000010f00
2025-03-20 01:14:38 - [command_handler:44] - INFO - 收到命令: 通信模式响应命令，客户端ID: 127.0.0.1:53706，命令代码: 0xF0
2025-03-20 01:14:38 - [command_handler:104] - INFO - <- 设备127.0.0.1:53706,回复0x F0，通信模式响应命令，设置: 01, 上报间隔: 15秒
2025-03-20 01:14:38 - [client_handler:59] - INFO - 从客户端 127.0.0.1:53706 接收到报文: faf50f80265200000000e9070314010e26ff8d
2025-03-20 01:14:38 - [command_handler:44] - INFO - 收到命令: 充电桩时间同步响应，客户端ID: 127.0.0.1:53706，命令代码: 0x52
2025-03-20 01:14:38 - [command_handler:110] - INFO - <- 设备127.0.0.1:53706,回复0x52，充电桩时间同步响应#，设置时间: 2025-03-20 01:14:38
2025-03-20 01:14:38 - [client_handler:59] - INFO - 从客户端 127.0.0.1:53706 接收到报文: faf509800b5800000000010059
2025-03-20 01:14:38 - [command_handler:44] - INFO - 收到命令: 充电桩心跳包信息上报，客户端ID: 127.0.0.1:53706，命令代码: 0x58
2025-03-20 01:14:38 - [command_handler:116] - INFO - <-- 收到充电桩: 127.0.0.1:53706，命令代码: 0x58，充电桩心跳包信息上报
2025-03-20 01:14:38 - [command_handler:121] - INFO - -> 向充电桩 127.0.0.1:53706 发送： 0x48
2025-03-20 01:14:38 - [client_handler:87] - INFO - 向客户端 127.0.0.1:53706 发送响应: faf50980264800000000010049
2025-03-20 01:14:40 - [client_handler:93] - INFO - 客户端 127.0.0.1:53706 断开连接
2025-03-20 01:21:34 - [client_handler:38] - INFO - 新客户端连接: 127.0.0.1:55283
2025-03-20 01:21:34 - [client_handler:59] - INFO - 从客户端 127.0.0.1:55283 接收到报文: faf53f802d1032060009700001000000000000000000000000000000000000000000082230001d010000800c000002000000e9070303092733ffe907030508182aff61
2025-03-20 01:21:34 - [command_handler:44] - INFO - 收到命令: 充电桩签到命令，客户端ID: 127.0.0.1:55283，命令代码: 0x10
2025-03-20 01:21:34 - [command_handler:56] - INFO - <-- 收到充电桩: 3206000970000100，命令代码: 0x10，充电桩签到命令
2025-03-20 01:21:34 - [command_handler:82] - INFO - -> 向充电桩 3206000970000100 发送： 0x09
2025-03-20 01:21:34 - [command_handler:89] - INFO - --> 向充电桩 3206000970000100 发送： 0xE0 ,通信模式设置命令 b'\x01'
2025-03-20 01:21:34 - [command_handler:97] - INFO - --> 向充电桩 3206000970000100 发送：0x42, 发送时间2025-03-20 01:21:34 同步命令
2025-03-20 01:21:34 - [client_handler:82] - INFO - 向客户端 127.0.0.1:55283 发送响应: faf52180220900000000320600097000010001000000010000113b3200010000113b0a0092
2025-03-20 01:21:34 - [client_handler:82] - INFO - 向客户端 127.0.0.1:55283 发送响应: faf5098022e000000000010ff0
2025-03-20 01:21:34 - [client_handler:82] - INFO - 向客户端 127.0.0.1:55283 发送响应: faf50f80224200000000e9070314011522ff80
2025-03-20 01:21:34 - [client_handler:59] - INFO - 从客户端 127.0.0.1:55283 接收到报文: faf5098022f000000000010f00
2025-03-20 01:21:34 - [command_handler:44] - INFO - 收到命令: 通信模式响应命令，客户端ID: 127.0.0.1:55283，命令代码: 0xF0
2025-03-20 01:21:34 - [command_handler:104] - INFO - <- 设备127.0.0.1:55283,回复0x F0，通信模式响应命令，设置: 01, 上报间隔: 15秒
2025-03-20 01:21:34 - [client_handler:59] - INFO - 从客户端 127.0.0.1:55283 接收到报文: faf50f80225200000000e9070314011522ff90
2025-03-20 01:21:34 - [command_handler:44] - INFO - 收到命令: 充电桩时间同步响应，客户端ID: 127.0.0.1:55283，命令代码: 0x52
2025-03-20 01:21:34 - [command_handler:110] - INFO - <- 设备127.0.0.1:55283,回复0x52，充电桩时间同步响应#，设置时间: 2025-03-20 01:21:34
2025-03-20 01:21:34 - [client_handler:59] - INFO - 从客户端 127.0.0.1:55283 接收到报文: faf509800b5800000000010059
2025-03-20 01:21:34 - [command_handler:44] - INFO - 收到命令: 充电桩心跳包信息上报，客户端ID: 127.0.0.1:55283，命令代码: 0x58
2025-03-20 01:21:34 - [command_handler:116] - INFO - <-- 收到充电桩: 127.0.0.1:55283，命令代码: 0x58，充电桩心跳包信息上报
2025-03-20 01:21:34 - [command_handler:121] - INFO - -> 向充电桩 127.0.0.1:55283 发送： 0x48
2025-03-20 01:21:34 - [client_handler:87] - INFO - 向客户端 127.0.0.1:55283 发送响应: faf50980224800000000010049
2025-03-20 01:21:36 - [client_handler:93] - INFO - 客户端 127.0.0.1:55283 断开连接
2025-03-20 01:24:08 - [client_handler:38] - INFO - 新客户端连接: 127.0.0.1:56318
2025-03-20 01:24:08 - [client_handler:59] - INFO - 从客户端 127.0.0.1:56318 接收到报文: faf53f802d1032060009700001000000000000000000000000000000000000000000082230001d010000800c000002000000e9070303092733ffe907030508182aff61
2025-03-20 01:24:08 - [command_handler:44] - INFO - 收到命令: 充电桩签到命令，客户端ID: 127.0.0.1:56318，命令代码: 0x10
2025-03-20 01:24:08 - [command_handler:56] - INFO - <-- 收到充电桩: 3206000970000100，命令代码: 0x10，充电桩签到命令
2025-03-20 01:24:08 - [command_handler:82] - INFO - -> 向充电桩 3206000970000100 发送： 0x09
2025-03-20 01:24:08 - [command_handler:89] - INFO - --> 向充电桩 3206000970000100 发送： 0xE0 ,通信模式设置命令 b'\x01'
2025-03-20 01:24:08 - [command_handler:97] - INFO - --> 向充电桩 3206000970000100 发送：0x42, 发送时间2025-03-20 01:24:08 同步命令
2025-03-20 01:24:08 - [client_handler:82] - INFO - 向客户端 127.0.0.1:56318 发送响应: faf52180080900000000320600097000010001000000010000113b3200010000113b0a0092
2025-03-20 01:24:08 - [client_handler:82] - INFO - 向客户端 127.0.0.1:56318 发送响应: faf5098008e000000000010ff0
2025-03-20 01:24:08 - [client_handler:82] - INFO - 向客户端 127.0.0.1:56318 发送响应: faf50f80084200000000e9070314011808ff69
2025-03-20 01:24:08 - [client_handler:59] - INFO - 从客户端 127.0.0.1:56318 接收到报文: faf5098008f000000000010f00
2025-03-20 01:24:08 - [command_handler:44] - INFO - 收到命令: 通信模式响应命令，客户端ID: 127.0.0.1:56318，命令代码: 0xF0
2025-03-20 01:24:08 - [command_handler:104] - INFO - <- 设备127.0.0.1:56318,回复0x F0，通信模式响应命令，设置: 01, 上报间隔: 15秒
2025-03-20 01:24:08 - [client_handler:59] - INFO - 从客户端 127.0.0.1:56318 接收到报文: faf50f80085200000000e9070314011808ff79
2025-03-20 01:24:08 - [command_handler:44] - INFO - 收到命令: 充电桩时间同步响应，客户端ID: 127.0.0.1:56318，命令代码: 0x52
2025-03-20 01:24:08 - [command_handler:110] - INFO - <- 设备127.0.0.1:56318,回复0x52，充电桩时间同步响应#，设置时间: 2025-03-20 01:24:08
2025-03-20 01:24:08 - [client_handler:59] - INFO - 从客户端 127.0.0.1:56318 接收到报文: faf509800b5800000000010059
2025-03-20 01:24:08 - [command_handler:44] - INFO - 收到命令: 充电桩心跳包信息上报，客户端ID: 127.0.0.1:56318，命令代码: 0x58
2025-03-20 01:24:08 - [command_handler:116] - INFO - <-- 收到充电桩: 127.0.0.1:56318，命令代码: 0x58，充电桩心跳包信息上报
2025-03-20 01:24:08 - [command_handler:121] - INFO - -> 向充电桩 127.0.0.1:56318 发送： 0x48
2025-03-20 01:24:08 - [client_handler:87] - INFO - 向客户端 127.0.0.1:56318 发送响应: faf50980084800000000010049
2025-03-20 01:24:10 - [client_handler:93] - INFO - 客户端 127.0.0.1:56318 断开连接
2025-03-20 01:26:26 - [client_handler:38] - INFO - 新客户端连接: 127.0.0.1:56781
2025-03-20 01:26:26 - [client_handler:59] - INFO - 从客户端 127.0.0.1:56781 接收到报文: faf53f802d1032060009700001000000000000000000000000000000000000000000082230001d010000800c000002000000e9070303092733ffe907030508182aff61
2025-03-20 01:26:26 - [command_handler:44] - INFO - 收到命令: 充电桩签到命令，客户端ID: 127.0.0.1:56781，命令代码: 0x10
2025-03-20 01:26:26 - [command_handler:56] - INFO - <-- 收到充电桩: 3206000970000100，命令代码: 0x10，充电桩签到命令
2025-03-20 01:26:26 - [command_handler:82] - INFO - -> 向充电桩 3206000970000100 发送： 0x09
2025-03-20 01:26:26 - [command_handler:89] - INFO - --> 向充电桩 3206000970000100 发送： 0xE0 ,通信模式设置命令 b'\x01'
2025-03-20 01:26:26 - [command_handler:97] - INFO - --> 向充电桩 3206000970000100 发送：0x42, 发送时间2025-03-20 01:26:26 同步命令
2025-03-20 01:26:26 - [client_handler:82] - INFO - 向客户端 127.0.0.1:56781 发送响应: faf521801a0900000000320600097000010001000000010000113b3200010000113b0a0092
2025-03-20 01:26:26 - [client_handler:82] - INFO - 向客户端 127.0.0.1:56781 发送响应: faf509801ae000000000010ff0
2025-03-20 01:26:26 - [client_handler:82] - INFO - 向客户端 127.0.0.1:56781 发送响应: faf50f801a4200000000e9070314011a1aff7d
2025-03-20 01:26:26 - [client_handler:59] - INFO - 从客户端 127.0.0.1:56781 接收到报文: faf509801af000000000010f00
2025-03-20 01:26:26 - [command_handler:44] - INFO - 收到命令: 通信模式响应命令，客户端ID: 127.0.0.1:56781，命令代码: 0xF0
2025-03-20 01:26:26 - [command_handler:104] - INFO - <- 设备127.0.0.1:56781,回复0x F0，通信模式响应命令，设置: 01, 上报间隔: 15秒
2025-03-20 01:26:26 - [client_handler:59] - INFO - 从客户端 127.0.0.1:56781 接收到报文: faf50f801a5200000000e9070314011a1aff8d
2025-03-20 01:26:26 - [command_handler:44] - INFO - 收到命令: 充电桩时间同步响应，客户端ID: 127.0.0.1:56781，命令代码: 0x52
2025-03-20 01:26:26 - [command_handler:110] - INFO - <- 设备127.0.0.1:56781,回复0x52，充电桩时间同步响应#，设置时间: 2025-03-20 01:26:26
2025-03-20 01:26:26 - [client_handler:59] - INFO - 从客户端 127.0.0.1:56781 接收到报文: faf509800b5800000000010059
2025-03-20 01:26:26 - [command_handler:44] - INFO - 收到命令: 充电桩心跳包信息上报，客户端ID: 127.0.0.1:56781，命令代码: 0x58
2025-03-20 01:26:26 - [command_handler:116] - INFO - <-- 收到充电桩: 127.0.0.1:56781，命令代码: 0x58，充电桩心跳包信息上报
2025-03-20 01:26:26 - [command_handler:121] - INFO - -> 向充电桩 127.0.0.1:56781 发送： 0x48
2025-03-20 01:26:26 - [client_handler:87] - INFO - 向客户端 127.0.0.1:56781 发送响应: faf509801a4800000000010049
2025-03-20 01:26:28 - [client_handler:93] - INFO - 客户端 127.0.0.1:56781 断开连接
2025-03-20 01:37:18 - [client_handler:38] - INFO - 新客户端连接: 127.0.0.1:59180
2025-03-20 01:37:18 - [client_handler:59] - INFO - 从客户端 127.0.0.1:59180 接收到报文: faf53f802d1032060009700001000000000000000000000000000000000000000000082230001d010000800c000002000000e9070303092733ffe907030508182aff61
2025-03-20 01:37:18 - [command_handler:44] - INFO - 收到命令: 充电桩签到命令，客户端ID: 127.0.0.1:59180，命令代码: 0x10
2025-03-20 01:37:18 - [command_handler:56] - INFO - <-- 收到充电桩: 3206000970000100，命令代码: 0x10，充电桩签到命令
2025-03-20 01:37:18 - [command_handler:82] - INFO - -> 向充电桩 3206000970000100 发送： 0x09
2025-03-20 01:37:18 - [command_handler:89] - INFO - --> 向充电桩 3206000970000100 发送： 0xE0 ,通信模式设置命令 b'\x01'
2025-03-20 01:37:18 - [command_handler:97] - INFO - --> 向充电桩 3206000970000100 发送：0x42, 发送时间2025-03-20 01:37:18 同步命令
2025-03-20 01:37:18 - [client_handler:82] - INFO - 向客户端 127.0.0.1:59180 发送响应: faf52180120900000000320600097000010001000000010000113b3200010000113b0a0092
2025-03-20 01:37:18 - [client_handler:82] - INFO - 向客户端 127.0.0.1:59180 发送响应: faf5098012e000000000010ff0
2025-03-20 01:37:18 - [client_handler:82] - INFO - 向客户端 127.0.0.1:59180 发送响应: faf50f80124200000000e9070314012512ff80
2025-03-20 01:37:18 - [client_handler:59] - INFO - 从客户端 127.0.0.1:59180 接收到报文: faf5098012f000000000010f00
2025-03-20 01:37:18 - [command_handler:44] - INFO - 收到命令: 通信模式响应命令，客户端ID: 127.0.0.1:59180，命令代码: 0xF0
2025-03-20 01:37:18 - [command_handler:104] - INFO - <- 设备127.0.0.1:59180,回复0x F0，通信模式响应命令，设置: 01, 上报间隔: 15秒
2025-03-20 01:37:18 - [client_handler:59] - INFO - 从客户端 127.0.0.1:59180 接收到报文: faf50f80125200000000e9070314012512ff90
2025-03-20 01:37:18 - [command_handler:44] - INFO - 收到命令: 充电桩时间同步响应，客户端ID: 127.0.0.1:59180，命令代码: 0x52
2025-03-20 01:37:18 - [command_handler:110] - INFO - <- 设备127.0.0.1:59180,回复0x52，充电桩时间同步响应#，设置时间: 2025-03-20 01:37:18
2025-03-20 01:37:18 - [client_handler:59] - INFO - 从客户端 127.0.0.1:59180 接收到报文: faf509800b5800000000010059
2025-03-20 01:37:18 - [command_handler:44] - INFO - 收到命令: 充电桩心跳包信息上报，客户端ID: 127.0.0.1:59180，命令代码: 0x58
2025-03-20 01:37:18 - [command_handler:116] - INFO - <-- 收到充电桩: 127.0.0.1:59180，命令代码: 0x58，充电桩心跳包信息上报
2025-03-20 01:37:18 - [command_handler:121] - INFO - -> 向充电桩 127.0.0.1:59180 发送： 0x48
2025-03-20 01:37:18 - [client_handler:87] - INFO - 向客户端 127.0.0.1:59180 发送响应: faf50980124800000000010049
2025-03-20 01:37:20 - [client_handler:93] - INFO - 客户端 127.0.0.1:59180 断开连接
2025-03-20 15:43:58 - [client_handler:38] - INFO - 新客户端连接: 127.0.0.1:57830
2025-03-20 15:43:58 - [client_handler:59] - INFO - 从客户端 127.0.0.1:57830 接收到报文: faf53f802d1032060009700001000000000000000000000000000000000000000000082230001d010000800c000002000000e9070303092733ffe907030508182aff61
2025-03-20 15:43:58 - [command_handler:44] - INFO - 收到命令: 充电桩签到命令，客户端ID: 127.0.0.1:57830，命令代码: 0x10
2025-03-20 15:43:58 - [command_handler:56] - INFO - <-- 收到充电桩: 3206000970000100，命令代码: 0x10，充电桩签到命令
2025-03-20 15:43:58 - [command_handler:82] - INFO - -> 向充电桩 3206000970000100 发送： 0x09
2025-03-20 15:43:58 - [command_handler:89] - INFO - --> 向充电桩 3206000970000100 发送： 0xE0 ,通信模式设置命令 b'\x01'
2025-03-20 15:43:58 - [command_handler:97] - INFO - --> 向充电桩 3206000970000100 发送：0x42, 发送时间2025-03-20 15:43:58 同步命令
2025-03-20 15:43:58 - [command_handler:106] - INFO - --> 向充电桩 3206000970000100 发送：0x0A, 设置参数: 1为:安徽卓越电气有限公司
2025-03-20 15:43:58 - [client_handler:82] - INFO - 向客户端 127.0.0.1:57830 发送响应: faf521803a0900000000320600097000010001000000010000113b3200010000113b0a0092
2025-03-20 15:43:58 - [client_handler:82] - INFO - 向客户端 127.0.0.1:57830 发送响应: faf509803ae000000000010ff0
2025-03-20 15:43:58 - [client_handler:82] - INFO - 向客户端 127.0.0.1:57830 发送响应: faf50f803a4200000000e90703140f2b3affbc
2025-03-20 15:43:58 - [client_handler:82] - INFO - 向客户端 127.0.0.1:57830 发送响应: faf56c803a0a0000000001b0b2bbd5d7bfd4bdb5e7c6f8d3d0cfdeb9abcbbe0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000bb
2025-03-20 15:43:58 - [client_handler:59] - INFO - 从客户端 127.0.0.1:57830 接收到报文: faf509803af000000000010f00
2025-03-20 15:43:58 - [command_handler:44] - INFO - 收到命令: 通信模式响应命令，客户端ID: 127.0.0.1:57830，命令代码: 0xF0
2025-03-20 15:43:58 - [command_handler:113] - INFO - <- 设备127.0.0.1:57830,回复0x F0，通信模式响应命令，设置: 01, 上报间隔: 15秒
2025-03-20 15:43:58 - [client_handler:59] - INFO - 从客户端 127.0.0.1:57830 接收到报文: faf50f803a5200000000e90703140f2b3affcc
2025-03-20 15:43:58 - [command_handler:44] - INFO - 收到命令: 充电桩时间同步响应，客户端ID: 127.0.0.1:57830，命令代码: 0x52
2025-03-20 15:43:58 - [command_handler:119] - INFO - <- 设备127.0.0.1:57830,回复0x52，充电桩时间同步响应#，设置时间: 2025-03-20 15:43:58
2025-03-20 15:43:58 - [client_handler:59] - INFO - 从客户端 127.0.0.1:57830 接收到报文: faf56e803a1a0000000001b0b2bbd5d7bfd4bdb5e7c6f8d3d0cfdeb9abcbbe00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000cb
2025-03-20 15:43:58 - [command_handler:44] - INFO - 收到命令: 充电桩运营参数设置响应命令，客户端ID: 127.0.0.1:57830，命令代码: 0x1A
2025-03-20 15:43:58 - [command_handler:138] - INFO - <- 设备127.0.0.1:57830,回复0x1A，设置参数: 公司信息为安徽卓越电气有限公司， 成功
2025-03-20 15:43:58 - [client_handler:59] - INFO - 从客户端 127.0.0.1:57830 接收到报文: faf509800b5800000000010059
2025-03-20 15:43:58 - [command_handler:44] - INFO - 收到命令: 充电桩心跳包信息上报，客户端ID: 127.0.0.1:57830，命令代码: 0x58
2025-03-20 15:43:58 - [command_handler:125] - INFO - <-- 收到充电桩: 127.0.0.1:57830，命令代码: 0x58，充电桩心跳包信息上报
2025-03-20 15:43:58 - [command_handler:130] - INFO - -> 向充电桩 127.0.0.1:57830 发送： 0x48
2025-03-20 15:43:58 - [client_handler:87] - INFO - 向客户端 127.0.0.1:57830 发送响应: faf509803a4800000000010049
2025-03-20 15:44:00 - [client_handler:93] - INFO - 客户端 127.0.0.1:57830 断开连接
