2025-03-19 11:07:17 - command_handler - INFO - 收到命令: 充电桩签到命令，客户端ID: 127.0.0.1:52277，命令代码: 0x10
2025-03-19 11:07:17 - command_handler - INFO - 向客户端 127.0.0.1:52277 发送通信模式设置命令
2025-03-19 11:10:08 - command_handler - INFO - 收到命令: 充电桩签到命令，客户端ID: 127.0.0.1:52786，命令代码: 0x10
2025-03-19 11:10:08 - command_handler - INFO - 向客户端 127.0.0.1:52786 发送通信模式设置命令
2025-03-19 11:10:08 - command_handler - INFO - 向客户端 127.0.0.1:52786 发送时间同步命令
2025-03-19 11:10:08 - command_handler - INFO - 收到命令: 通信模式响应命令，客户端ID: 127.0.0.1:52786，命令代码: 0xF0
2025-03-19 13:44:28 - command_handler - INFO - 收到命令: 充电桩签到命令，客户端ID: 127.0.0.1:58993，命令代码: 0x10
2025-03-19 13:44:28 - command_handler - INFO - 向客户端 127.0.0.1:58993 发送通信模式设置命令
2025-03-19 13:44:28 - command_handler - INFO - 向客户端 127.0.0.1:58993 发送时间同步命令
2025-03-19 13:44:28 - command_handler - INFO - 收到命令: 通信模式响应命令，客户端ID: 127.0.0.1:58993，命令代码: 0xF0
2025-03-19 13:48:33 - command_handler - INFO - 收到命令: 充电桩签到命令，客户端ID: 127.0.0.1:59158，命令代码: 0x10
2025-03-19 13:48:33 - command_handler - INFO - 向客户端 127.0.0.1:59158 发送通信模式设置命令
2025-03-19 13:48:33 - command_handler - INFO - 向客户端 127.0.0.1:59158 发送时间同步命令
2025-03-19 13:48:33 - command_handler - INFO - 收到命令: 通信模式响应命令，客户端ID: 127.0.0.1:59158，命令代码: 0xF0
2025-03-19 13:52:38 - command_handler - INFO - 收到命令: 充电桩签到命令，客户端ID: 127.0.0.1:59486，命令代码: 0x10
2025-03-19 13:52:38 - command_handler - INFO - 向客户端 127.0.0.1:59486 发送通信模式设置命令
2025-03-19 13:52:38 - command_handler - INFO - 向客户端 127.0.0.1:59486 发送时间同步命令
2025-03-19 13:52:38 - command_handler - INFO - 收到命令: 通信模式响应命令，客户端ID: 127.0.0.1:59486，命令代码: 0xF0
2025-03-19 13:52:38 - command_handler - INFO - 收到命令: 充电桩时间同步响应，客户端ID: 127.0.0.1:59486，命令代码: 0x52
2025-03-19 14:20:08 - command_handler - INFO - <-- 收到充电桩: b'2\x06\x00\tp\x00\x01\x00'，命令代码: 0x10，命令主题：充电桩签到命令
2025-03-19 14:20:08 - command_handler - INFO - -> 向充电桩 b'2\x06\x00\tp\x00\x01\x00' 发送： 224 ,通信模式设置命令 b'\x01'
2025-03-19 14:49:53 - command_handler - INFO - <-- 收到充电桩: 3206000970000100，命令代码: 0x10，充电桩签到命令
2025-03-19 14:49:53 - command_handler - INFO - -> 向充电桩 3206000970000100 发送： 0xE0 ,通信模式设置命令 b'\x01'
2025-03-19 14:49:53 - command_handler - INFO - -> 向充电桩 b'2\x06\x00\tp\x00\x01\x00' 发送：0x42, 发送时间2025-03-19 14:49:53 同步命令
2025-03-19 15:13:23 - command_handler - INFO - <-- 收到充电桩: 3206000970000100，命令代码: 0x10，充电桩签到命令
2025-03-19 15:13:23 - command_handler - INFO - -> 向充电桩 3206000970000100 发送： 0xE0 ,通信模式设置命令 b'\x01'
2025-03-19 15:13:23 - command_handler - INFO - -> 向充电桩 b'2\x06\x00\tp\x00\x01\x00' 发送：0x42, 发送时间2025-03-19 15:13:23 同步命令
2025-03-19 15:14:49 - command_handler - INFO - <-- 收到充电桩: 3206000970000100，命令代码: 0x10，充电桩签到命令
2025-03-19 15:14:49 - command_handler - INFO - -> 向充电桩 3206000970000100 发送： 0x09
2025-03-19 15:14:49 - command_handler - INFO - -> 向充电桩 3206000970000100 发送： 0xE0 ,通信模式设置命令 b'\x01'
2025-03-19 15:14:49 - command_handler - INFO - -> 向充电桩 3206000970000100 发送：0x42, 发送时间2025-03-19 15:14:49 同步命令
2025-03-19 15:20:40 - command_handler - INFO - <-- 收到充电桩: 3206000970000100，命令代码: 0x10，充电桩签到命令
2025-03-19 15:20:40 - command_handler - INFO - -> 向充电桩 3206000970000100 发送： 0x09
2025-03-19 15:20:40 - command_handler - INFO - -> 向充电桩 3206000970000100 发送： 0xE0 ,通信模式设置命令 b'\x01'
2025-03-19 15:20:40 - command_handler - INFO - -> 向充电桩 3206000970000100 发送：0x42, 发送时间2025-03-19 15:20:40 同步命令
2025-03-19 15:24:09 - command_handler - INFO - <-- 收到充电桩: 3206000970000100，命令代码: 0x10，充电桩签到命令
2025-03-19 15:24:09 - command_handler - INFO - -> 向充电桩 3206000970000100 发送： 0x09
2025-03-19 15:24:09 - command_handler - INFO - -> 向充电桩 3206000970000100 发送： 0xE0 ,通信模式设置命令 b'\x01'
2025-03-19 15:24:09 - command_handler - INFO - -> 向充电桩 3206000970000100 发送：0x42, 发送时间2025-03-19 15:24:09 同步命令
2025-03-19 15:30:11 - command_handler - INFO - 收到命令: 充电桩签到命令，客户端ID: 127.0.0.1:64549，命令代码: 0x10
2025-03-19 15:30:11 - command_handler - INFO - <-- 收到充电桩: 3206000970000100，命令代码: 0x10，充电桩签到命令
2025-03-19 15:30:11 - command_handler - INFO - -> 向充电桩 3206000970000100 发送： 0x09
2025-03-19 15:30:11 - command_handler - INFO - -> 向充电桩 3206000970000100 发送： 0xE0 ,通信模式设置命令 b'\x01'
2025-03-19 15:30:11 - command_handler - INFO - -> 向充电桩 3206000970000100 发送：0x42, 发送时间2025-03-19 15:30:11 同步命令
2025-03-19 15:30:11 - command_handler - INFO - 收到命令: 通信模式响应命令，客户端ID: 127.0.0.1:64549，命令代码: 0xF0
2025-03-19 15:30:11 - command_handler - INFO - 收到命令: 充电桩时间同步响应，客户端ID: 127.0.0.1:64549，命令代码: 0x52
2025-03-19 15:36:31 - command_handler - INFO - 收到命令: 充电桩签到命令，客户端ID: 127.0.0.1:64776，命令代码: 0x10
2025-03-19 15:36:31 - command_handler - INFO - <-- 收到充电桩: 3206000970000100，命令代码: 0x10，充电桩签到命令
2025-03-19 15:36:31 - command_handler - INFO - -> 向充电桩 3206000970000100 发送： 0x09
2025-03-19 15:36:31 - command_handler - INFO - -> 向充电桩 3206000970000100 发送： 0xE0 ,通信模式设置命令 b'\x01'
2025-03-19 15:36:31 - command_handler - INFO - -> 向充电桩 3206000970000100 发送：0x42, 发送时间2025-03-19 15:36:31 同步命令
2025-03-19 15:36:31 - command_handler - INFO - 收到命令: 通信模式响应命令，客户端ID: 127.0.0.1:64776，命令代码: 0xF0
2025-03-19 15:36:31 - command_handler - INFO - 收到 0xF0
2025-03-19 15:36:31 - command_handler - INFO - <- 0x F0，通信模式响应命令，设置: 01, 上报间隔: 15秒
2025-03-19 15:36:31 - command_handler - INFO - 收到命令: 充电桩时间同步响应，客户端ID: 127.0.0.1:64776，命令代码: 0x52
2025-03-19 15:36:31 - command_handler - INFO - 收到 0x52
2025-03-19 15:38:48 - command_handler - INFO - 收到命令: 充电桩签到命令，客户端ID: 127.0.0.1:65003，命令代码: 0x10
2025-03-19 15:38:48 - command_handler - INFO - <-- 收到充电桩: 3206000970000100，命令代码: 0x10，充电桩签到命令
2025-03-19 15:38:48 - command_handler - INFO - -> 向充电桩 3206000970000100 发送： 0x09
2025-03-19 15:38:48 - command_handler - INFO - -> 向充电桩 3206000970000100 发送： 0xE0 ,通信模式设置命令 b'\x01'
2025-03-19 15:38:48 - command_handler - INFO - -> 向充电桩 3206000970000100 发送：0x42, 发送时间2025-03-19 15:38:48 同步命令
2025-03-19 15:38:48 - command_handler - INFO - 收到命令: 通信模式响应命令，客户端ID: 127.0.0.1:65003，命令代码: 0xF0
2025-03-19 15:38:48 - command_handler - INFO - 收到 0xF0
2025-03-19 15:38:48 - command_handler - INFO - <- 0x F0，通信模式响应命令，设置: 01, 上报间隔: 15秒
2025-03-19 15:38:48 - command_handler - INFO - 收到命令: 充电桩时间同步响应，客户端ID: 127.0.0.1:65003，命令代码: 0x52
2025-03-19 15:38:48 - command_handler - INFO - 收到 0x52
2025-03-19 15:38:48 - command_handler - INFO - <- 0x 52，充电桩时间同步响应#，设置时间: 2025-03-19 15:38:48
2025-03-19 17:45:53 - command_handler - INFO - 收到命令: 充电桩签到命令，客户端ID: 127.0.0.1:55004，命令代码: 0x10
2025-03-19 17:45:53 - command_handler - INFO - <-- 收到充电桩: 3206000970000100，命令代码: 0x10，充电桩签到命令
2025-03-19 17:45:53 - command_handler - INFO - -> 向充电桩 3206000970000100 发送： 0x09
2025-03-19 17:45:53 - command_handler - INFO - --> 向充电桩 3206000970000100 发送： 0xE0 ,通信模式设置命令 b'\x01'
2025-03-19 17:45:53 - command_handler - INFO - --> 向充电桩 3206000970000100 发送：0x42, 发送时间2025-03-19 17:45:53 同步命令
2025-03-19 17:45:53 - command_handler - INFO - 收到命令: 通信模式响应命令，客户端ID: 127.0.0.1:55004，命令代码: 0xF0
2025-03-19 17:45:53 - command_handler - INFO - <- 设备127.0.0.1:55004,回复0x F0，通信模式响应命令，设置: 01, 上报间隔: 15秒
2025-03-19 17:45:53 - command_handler - INFO - 收到命令: 充电桩时间同步响应，客户端ID: 127.0.0.1:55004，命令代码: 0x52
2025-03-19 17:45:53 - command_handler - INFO - <- 设备127.0.0.1:55004,回复0x 52，充电桩时间同步响应#，设置时间: 2025-03-19 17:45:53
2025-03-19 17:52:41 - command_handler - INFO - 收到命令: 充电桩签到命令，客户端ID: 127.0.0.1:55489，命令代码: 0x10
2025-03-19 17:52:41 - command_handler - INFO - <-- 收到充电桩: 3206000970000100，命令代码: 0x10，充电桩签到命令
2025-03-19 17:52:41 - command_handler - INFO - -> 向充电桩 3206000970000100 发送： 0x09
2025-03-19 17:52:41 - command_handler - INFO - --> 向充电桩 3206000970000100 发送： 0xE0 ,通信模式设置命令 b'\x01'
2025-03-19 17:52:41 - command_handler - INFO - --> 向充电桩 3206000970000100 发送：0x42, 发送时间2025-03-19 17:52:41 同步命令
2025-03-19 17:52:41 - command_handler - INFO - 收到命令: 通信模式响应命令，客户端ID: 127.0.0.1:55489，命令代码: 0xF0
2025-03-19 17:52:41 - command_handler - INFO - <- 设备127.0.0.1:55489,回复0x F0，通信模式响应命令，设置: 01, 上报间隔: 15秒
2025-03-19 17:52:41 - command_handler - INFO - 收到命令: 充电桩时间同步响应，客户端ID: 127.0.0.1:55489，命令代码: 0x52
2025-03-19 17:52:41 - command_handler - INFO - <- 设备127.0.0.1:55489,回复0x 52，充电桩时间同步响应#，设置时间: 2025-03-19 17:52:41
2025-03-19 23:01:55 - command_handler - INFO - 收到命令: 充电桩签到命令，客户端ID: 127.0.0.1:51520，命令代码: 0x10
2025-03-19 23:01:55 - command_handler - INFO - <-- 收到充电桩: 3206000970000100，命令代码: 0x10，充电桩签到命令
2025-03-19 23:01:55 - command_handler - INFO - -> 向充电桩 3206000970000100 发送： 0x09
2025-03-19 23:01:55 - command_handler - INFO - --> 向充电桩 3206000970000100 发送： 0xE0 ,通信模式设置命令 b'\x01'
2025-03-19 23:01:55 - command_handler - INFO - --> 向充电桩 3206000970000100 发送：0x42, 发送时间2025-03-19 23:01:55 同步命令
2025-03-19 23:01:55 - command_handler - INFO - 收到命令: 通信模式响应命令，客户端ID: 127.0.0.1:51520，命令代码: 0xF0
2025-03-19 23:01:55 - command_handler - INFO - <- 设备127.0.0.1:51520,回复0x F0，通信模式响应命令，设置: 01, 上报间隔: 15秒
2025-03-19 23:01:55 - command_handler - INFO - 收到命令: 充电桩时间同步响应，客户端ID: 127.0.0.1:51520，命令代码: 0x52
2025-03-19 23:01:55 - command_handler - INFO - <- 设备127.0.0.1:51520,回复0x 52，充电桩时间同步响应#，设置时间: 2025-03-19 23:01:55
2025-03-19 23:01:55 - command_handler - INFO - 收到命令: 服务器系统心跳包信息应答，客户端ID: 127.0.0.1:51520，命令代码: 0x48
2025-03-19 23:06:31 - command_handler - INFO - 收到命令: 充电桩签到命令，客户端ID: 127.0.0.1:51563，命令代码: 0x10
2025-03-19 23:06:31 - command_handler - INFO - <-- 收到充电桩: 3206000970000100，命令代码: 0x10，充电桩签到命令
2025-03-19 23:06:31 - command_handler - INFO - -> 向充电桩 3206000970000100 发送： 0x09
2025-03-19 23:06:31 - command_handler - INFO - --> 向充电桩 3206000970000100 发送： 0xE0 ,通信模式设置命令 b'\x01'
2025-03-19 23:06:31 - command_handler - INFO - --> 向充电桩 3206000970000100 发送：0x42, 发送时间2025-03-19 23:06:31 同步命令
2025-03-19 23:06:31 - command_handler - INFO - 收到命令: 通信模式响应命令，客户端ID: 127.0.0.1:51563，命令代码: 0xF0
2025-03-19 23:06:31 - command_handler - INFO - <- 设备127.0.0.1:51563,回复0x F0，通信模式响应命令，设置: 01, 上报间隔: 15秒
2025-03-19 23:06:31 - command_handler - INFO - 收到命令: 充电桩时间同步响应，客户端ID: 127.0.0.1:51563，命令代码: 0x52
2025-03-19 23:06:31 - command_handler - INFO - <- 设备127.0.0.1:51563,回复0x 52，充电桩时间同步响应#，设置时间: 2025-03-19 23:06:31
2025-03-19 23:06:31 - command_handler - INFO - 收到命令: 充电桩心跳包信息上报，客户端ID: 127.0.0.1:51563，命令代码: 0x58
2025-03-19 23:06:31 - command_handler - INFO - <-- 收到充电桩: 127.0.0.1:51563，命令代码: 0x58，充电桩心跳包信息上报
2025-03-19 23:07:08 - command_handler - INFO - 收到命令: 充电桩签到命令，客户端ID: 127.0.0.1:51567，命令代码: 0x10
2025-03-19 23:07:08 - command_handler - INFO - <-- 收到充电桩: 3206000970000100，命令代码: 0x10，充电桩签到命令
2025-03-19 23:07:08 - command_handler - INFO - -> 向充电桩 3206000970000100 发送： 0x09
2025-03-19 23:07:08 - command_handler - INFO - --> 向充电桩 3206000970000100 发送： 0xE0 ,通信模式设置命令 b'\x01'
2025-03-19 23:07:08 - command_handler - INFO - --> 向充电桩 3206000970000100 发送：0x42, 发送时间2025-03-19 23:07:08 同步命令
2025-03-19 23:07:08 - command_handler - INFO - 收到命令: 通信模式响应命令，客户端ID: 127.0.0.1:51567，命令代码: 0xF0
2025-03-19 23:07:08 - command_handler - INFO - <- 设备127.0.0.1:51567,回复0x F0，通信模式响应命令，设置: 01, 上报间隔: 15秒
2025-03-19 23:07:08 - command_handler - INFO - 收到命令: 充电桩时间同步响应，客户端ID: 127.0.0.1:51567，命令代码: 0x52
2025-03-19 23:07:08 - command_handler - INFO - <- 设备127.0.0.1:51567,回复0x 52，充电桩时间同步响应#，设置时间: 2025-03-19 23:07:08
2025-03-19 23:07:08 - command_handler - INFO - 收到命令: 充电桩心跳包信息上报，客户端ID: 127.0.0.1:51567，命令代码: 0x58
2025-03-19 23:07:08 - command_handler - INFO - <-- 收到充电桩: 127.0.0.1:51567，命令代码: 0x58，充电桩心跳包信息上报
