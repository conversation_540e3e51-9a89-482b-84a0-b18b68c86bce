#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
运营参数设置响应报文模块

该模块实现了充电桩对运营参数设置的响应报文类。
"""

import logging
from typing import Dict, List, Optional, Tuple

from .base import BaseMessage, Command


class OperationParamsResponseMessage(BaseMessage):
    """
    运营参数设置响应报文类
    """

    #COMMAND_CODE = 0x1A  # Command.OPERATION_PARAMS_RESPONSE.value
    COMMAND_CODE = Command.OPERATION_PARAMS_RESPONSE.value

    def __init__(self):
        """
        初始化运营参数设置响应报文
        """
        super().__init__()
        self.user_id = 0
        self.command_sequence = 0
        self.param_type = 0  # 参数类型
        self.param_content = ""  # 参数内容,按照 GB2312 中汉字编码方式共占用100个字节；文本描述长度不足时， 采用 0x00 补足
        self.result = 0  # 设置结果，0表示成功，1表示失败

    def get_param_type_description(self):
        """ 根据 param_type 返回对应的参数类型描述 """
        param_mapping = {
            1: "公司信息",
            2: "网站信息",
            3: "客服电话",
            4: "本网点收费信息",
            5: "充电接口信息",
            6: "注意事项",
            7: "二维码动态字段"
        }
        return param_mapping.get(self.param_type, "未知参数类型")  # 默认返回 "未知参数类型"
    
    # def set_param_info(self, user_id: int, command_sequence: int):
    #     """
    #     设置基础参数信息
    #
    #     Args:
    #         user_id: 用户ID
    #         command_sequence: 指令序号
    #     """
    #     self.user_id = user_id
    #     self.command_sequence = command_sequence


    def set_operation_result_info(self, param_type: int, param_content: str, result: int):
        """
        设置运营参数信息

        Args:
            param_type: 参数类型
                1: 公司信息
                2: 网站信息
                3: 客服电话
                4: 本网点收费信息
                5: 充电接口信息
                6: 注意事项
                7: 二维码动态首字段
            param_content: 参数内容
        """
        self.param_type = param_type
        self.param_content = param_content
        self.result = result
    
    def encode(self) -> bytes:
        """
        编码运营参数设置响应报文
        
        Returns:
            编码后的完整报文字节数据
        """
        data = bytearray()

        # 1. 用户ID (2字节)
        data.extend(self.user_id.to_bytes(2, byteorder='little'))

        # 2. 指令序号 (2字节)
        data.extend(self.command_sequence.to_bytes(2, byteorder='little'))
        
        # 3. 参数类型 (1字节)
        data.append(self.param_type)

        # 4. 参数内容 (根据GB/T 32960.2中文字编码方式,100字节)
        # 将字符串转换为GB2312编码的字节
        try:
            content_bytes = self.param_content.encode('gb2312')
            if len(content_bytes)<100:
                content_bytes = content_bytes.ljust(100, b'\x00')
            else:
                content_bytes = content_bytes[:100]
            data.extend(content_bytes)
        except UnicodeEncodeError as e:
            logging.error(f"参数内容编码错误: {e}")
            # 如果编码失败，使用空字符串
            data.extend(b'')
        
        # 5. 设置结果 (2字节)
        data.extend(self.result.to_bytes(2, byteorder='little'))
        
        # 创建完整报文
        return self.create_message(data)
    
    @classmethod
    def decode(cls, message: bytes) -> Optional['OperationParamsResponseMessage']:
        """
        解码运营参数设置响应报文
        
        Args:
            message: 完整的报文字节数据
            
        Returns:
            解码后的运营参数设置响应报文对象，如果解码失败则返回None
        """
        # 解析报文头部
        header = cls.parse_header(message)
        if not header or header['command_code'] != cls.COMMAND_CODE:
            return None
        
        # 创建运营参数设置响应报文对象
        response_message = cls()
        response_message.sequence_number = header['sequence_number']
        
        # 解析数据域
        data = header['data']
        if len(data) < 106:  # 最小数据长度检查：用户ID(2) + 指令序号(2) + 参数类型(1)  + 内容（100）+ 设置结果(1)
            logging.warning("运营参数设置响应命令数据域长度不足")
            return None
        
        try:
            index = 0
            
            # 1. 用户ID (2字节)
            response_message.user_id = int.from_bytes(data[index:index+2], byteorder='little')
            index += 2
            
            # 2. 指令序号 (2字节)
            response_message.command_sequence = int.from_bytes(data[index:index+2], byteorder='little')
            index += 2
            
            # 3. 参数类型 (1字节)
            response_message.param_type = data[index]
            index += 1

            # 4. 参数内容 (剩余字节)
            try:
                response_message.param_content = data[index:].decode('gb2312').rstrip('\x00')
            except UnicodeDecodeError:
                logging.warning("参数内容解码失败，可能不是有效的GB2312编码")
                response_message.param_content = ""
            index += 100
            
            # 5. 设置结果 (1字节)
            response_message.result =  int.from_bytes(data[index:index+2], byteorder='little')
            
            return response_message
            
        except Exception as e:
            logging.error(f"解析运营参数设置响应报文时出错: {e}")
            return None