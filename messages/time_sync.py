"""
通信模式设置报文模块

该模块实现了通信模式设置报文类。
"""

import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime

from .base import BaseMessage, Command

class TimeSync(BaseMessage):
    """
    通信模式设置报文类
    """

    COMMAND_CODE = Command.TIME_SYNC.value

    def __init__(self):
        """
        时间同步设置报文
        """
        super().__init__()
        self.user_id = 0
        self.command_sequence = 0
        self.year = 2016
        self.month = 12  # 默认上报间隔为15秒
        self.day = 20
        self.hour = 13
        self.minute = 13
        self.second = 13
        self.keep = b'\xFF'

    def set_info(self, user_id: int, command_sequence: int):
        """
        Args:
            user_id: 用户ID
            command_sequence: 指令序号
        """
        self.user_id = user_id
        self.command_sequence = command_sequence

    def set_time(self):
        now = datetime.now()
        self.year = now.year
        self.month = now.month
        self.day = now.day
        self.hour = now.hour
        self.minute = now.minute
        self.second = now.second

    def encode(self) -> bytes:
        """
        编码通信模式设置报文

        Returns:
            编码后的完整报文字节数据
        """
        data = bytearray()
        self.set_time()  #使用当前时间

        # 1. 用户ID (2字节)
        data.extend(self.user_id.to_bytes(2, byteorder='little'))

        # 2. 指令序号 (2字节)
        data.extend(self.command_sequence.to_bytes(2, byteorder='little'))

        data.extend(self.year.to_bytes(2, byteorder='little'))
        data.append(self.month)
        data.append(self.day)
        data.append(self.hour)
        data.append(self.minute)
        data.append(self.second)
        data.extend(self.keep)

        # 创建完整报文
        return self.create_message(data)

    @classmethod
    def decode(cls, message: bytes) -> Optional['TimeSync']:
        """
        解码通信模式响应报文

        Args:
            message: 完整的报文字节数据

        Returns:
            解码后的通信模式响应报文对象，如果解码失败则返回None
        """
        # 解析报文头部
        header = cls.parse_header(message)
        if not header or header['command_code'] != cls.COMMAND_CODE:
            return None

        # 创建通信模式响应报文对象
        response_message = cls()
        response_message.sequence_number = header['sequence_number']

        # 解析数据域
        data = header['data']
        if len(data) < 12:  # 最小数据长度检查
            logging.warning("时间同步命令数据域长度不足")
            return None

        try:
            index = 0

            # 1. 用户ID (2字节)
            response_message.user_id = int.from_bytes(data[index:index + 2], byteorder='little')
            index += 2

            # 2. 指令序号 (2字节)
            response_message.command_sequence = int.from_bytes(data[index:index + 2], byteorder='little')
            index += 2

            # 3. 解析时间
            response_message.year = int.from_bytes(data[index:index + 2], byteorder='little')
            index += 2

            response_message.month = data[index]
            index += 1

            response_message.day = data[index]
            index += 1

            response_message.hour = data[index]
            index += 1

            response_message.minute = data[index]
            index += 1

            response_message.second = data[index]
            index += 1

            return response_message

        except Exception as e:
            logging.error(f"解析时间同步报文数据域时出错: {e}")
            return None

