#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
通信模式设置报文模块

该模块实现了通信模式设置报文类。
"""

import logging
from typing import Dict, List, Optional, Tuple

from .base import BaseMessage, Command


class CommunicationMode(BaseMessage):
    """
    通信模式设置报文类
    """

    COMMAND_CODE = Command.COMMUNICATION_MODE_SET.value

    def __init__(self):
        """
        初始化通信模式设置报文
        """
        super().__init__()
        self.user_id = 0
        self.command_sequence = 0
        self.communication_mode = b'\x01'  # 默认通信模式为1
        self.interval = 15  # 默认上报间隔为15秒

    def set_basic_info(self, user_id: int, command_sequence: int):
        """
        设置信息

        Args:
            user_id: 用户ID
            command_sequence: 指令序号
        """
        self.user_id = user_id
        self.command_sequence = command_sequence

    def set_communication_info(self, commu_mode: bytes, interval: int):
        """
        设置通信模式信息

        Args:
            commu_mode: 通信模式，1字节
            interval: 定时上报间隔，单位：秒
        """
        self.communication_mode = commu_mode
        self.interval = interval

    def encode(self) -> bytes:
        """
        编码通信模式设置报文
        
        Returns:
            编码后的完整报文字节数据
        """
        data = bytearray()

        # 1. 用户ID (2字节)
        data.extend(self.user_id.to_bytes(2, byteorder='little'))

        # 2. 指令序号 (2字节)
        data.extend(self.command_sequence.to_bytes(2, byteorder='little'))
        
        # 3. 通信模式 (1字节)
        data.extend(self.communication_mode)
        
        # 4. 定时上报间隔 (1字节，单位：秒)
        data.append(self.interval)
        
        # 创建完整报文
        return self.create_message(data)
    
    @classmethod
    def decode(cls, message: bytes) -> Optional['CommunicationMode']:
        """
        解码通信模式设置报文
        
        Args:
            message: 完整的报文字节数据
            
        Returns:
            解码后的通信模式设置报文对象，如果解码失败则返回None
        """
        # 解析报文头部
        header = cls.parse_header(message)
        if not header or header['command_code'] != cls.COMMAND_CODE:
            return None
        
        # 创建通信模式设置报文对象
        commu_mode_message = cls()
        commu_mode_message.sequence_number = header['sequence_number']
        
        # 解析数据域
        data = header['data']
        if len(data) < 6:  # 最小数据长度检查：用户ID(2) + 指令序号(2) + 通信模式(1) + 定时上报间隔(1)
            logging.warning("通信模式设置命令数据域长度不足")
            return None
        
        try:
            index = 0
            
            # 1. 用户ID (2字节)
            commu_mode_message.user_id = int.from_bytes(data[index:index+2], byteorder='little')
            index += 2
            
            # 2. 指令序号 (2字节)
            commu_mode_message.command_sequence = int.from_bytes(data[index:index+2], byteorder='little')
            index += 2
            
            # 3. 通信模式 (1字节)
            commu_mode_message.communication_mode = bytes([data[index]])
            index += 1
            
            # 4. 定时上报间隔 (1字节)
            commu_mode_message.interval = data[index]
            
            return commu_mode_message
            
        except Exception as e:
            logging.error(f"解析通信模式设置报文数据域时出错: {e}")
            return None