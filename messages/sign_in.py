#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
充电桩签到报文模块

该模块实现了充电桩签到报文类。
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple

from .base import BaseMessage, Command


class SignInMessage(BaseMessage):
    """
    充电桩签到报文类
    """
    
    COMMAND_CODE = Command.CHARGING_PILE_SIGN_IN.value
    
    def __init__(self):
        """
        初始化签到报文
        """
        super().__init__()
        self.charging_pile_code = b'\x00' * 8
        self.system_asset_code = b'\x00' * 20
        self.system_version = (1, 0, 0, 0)
        self.start_count = 1
        self.storage_capacity = 1024
        self.running_time = 0
        self.start_time = datetime.now()
        self.sign_in_time = datetime.now()
    
    def set_charging_pile_code(self,charging_pile_code: bytes):
        """
        设置充电桩编码

        Args:
            charging_pile_code: 充电桩编码
        """
        self.charging_pile_code = charging_pile_code
    
    def set_system_info(self, system_asset_code: bytes, system_version: Tuple[int, int, int, int],
                       start_count: int, storage_capacity: int, running_time: int):
        """
        设置系统信息
        
        Args:
            system_asset_code: 系统设备资产编码
            system_version: 系统软件版本
            start_count: 启动次数
            storage_capacity: 存储空间容量
            running_time: 充电桩软件已经持续运行时间
        """
        self.system_asset_code = system_asset_code
        self.system_version = system_version
        self.start_count = start_count
        self.storage_capacity = storage_capacity
        self.running_time = running_time
    
    def set_time_info(self, start_time: datetime, sign_in_time: datetime):
        """
        设置时间信息
        
        Args:
            start_time: 最近一次启动时间
            sign_in_time: 最近一次签到时间
        """
        self.start_time = start_time
        self.sign_in_time = sign_in_time
    
    def encode(self) -> bytes:
        """
        编码签到报文
        
        Returns:
            编码后的完整报文字节数据
        """
        data = bytearray()
        
        # 3. 充电桩编码 (8字节)
        data.extend(self.charging_pile_code)
        
        # 4. 系统设备资产编码 (20字节，ASCII码)
        data.extend(self.system_asset_code)
        
        # 5. 系统软件版本 (4字节)
        data.extend(bytes(self.system_version))
        
        # 6. 启动次数 (4字节)
        data.extend(self.start_count.to_bytes(4, byteorder='little'))
        
        # 7. 存储空间容量 (4字节，单位：MB)
        data.extend(self.storage_capacity.to_bytes(4, byteorder='little'))
        
        # 8. 充电桩软件已经持续运行时间 (4字节，单位：分钟)
        data.extend(self.running_time.to_bytes(4, byteorder='little'))
        
        # 9. 最近一次启动时间 (8字节，格式：年-月-日-时-分-秒)
        # 年份用2字节表示，根据实际年份动态计算
        year_bytes = self.start_time.year.to_bytes(2, byteorder='big')
        start_time_bytes = bytes([
            year_bytes[0],  # 年份高字节
            year_bytes[1],  # 年份低字节
            self.start_time.month,  # 月
            self.start_time.day,     # 日
            self.start_time.hour,    # 时
            self.start_time.minute,  # 分
            self.start_time.second,  # 秒
            0   # 预留
        ])
        data.extend(start_time_bytes)
        
        # 10. 最近一次签到时间 (8字节，格式：年-月-日-时-分-秒)
        # 年份用2字节表示，根据实际年份动态计算
        year_bytes = self.sign_in_time.year.to_bytes(2, byteorder='big')
        sign_in_time_bytes = bytes([
            year_bytes[0],  # 年份高字节
            year_bytes[1],  # 年份低字节
            self.sign_in_time.month,  # 月
            self.sign_in_time.day,     # 日
            self.sign_in_time.hour,    # 时
            self.sign_in_time.minute,  # 分
            self.sign_in_time.second,  # 秒
            0   # 预留
        ])
        data.extend(sign_in_time_bytes)
        
        # 创建完整报文
        return self.create_message(data)
    
    @classmethod
    def decode(cls, message: bytes) -> Optional['SignInMessage']:
        """
        解码签到报文
        
        Args:
            message: 完整的报文字节数据
            
        Returns:
            解码后的签到报文对象，如果解码失败则返回None
        """
        # 解析报文头部
        header = cls.parse_header(message)
        if not header or header['command_code'] != cls.COMMAND_CODE:
            return None
        
        # 创建签到报文对象
        sign_in_message = cls()
        sign_in_message.sequence_number = header['sequence_number']
        
        # 解析数据域
        data = header['data']
        if len(data) < 60:  # 最小数据长度检查
            logging.warning("签到命令数据域长度不足")
            return None
        
        try:
            index = 0

            
            # 充电桩编码 (8字节)
            sign_in_message.charging_pile_code = data[index:index+8]
            index += 8
            
            # 系统设备资产编码 (20字节)
            sign_in_message.system_asset_code = data[index:index+20]
            index += 20
            
            # 系统软件版本 (4字节)
            sign_in_message.system_version = tuple(data[index:index+4])
            index += 4
            
            # 启动次数 (4字节)
            sign_in_message.start_count = int.from_bytes(data[index:index+4], byteorder='little')
            index += 4
            
            # 存储空间容量 (4字节)
            sign_in_message.storage_capacity = int.from_bytes(data[index:index+4], byteorder='little')
            index += 4
            
            # 充电桩软件已经持续运行时间 (4字节)
            sign_in_message.running_time = int.from_bytes(data[index:index+4], byteorder='little')
            index += 4
            
            # 最近一次启动时间 (8字节)
            start_time_data = data[index:index+8]
            # 从字节数据中解析年份
            year = int.from_bytes(start_time_data[0:2], byteorder='big')
            sign_in_message.start_time = datetime(
                year,                   # 从字节数据中解析的年份
                start_time_data[2],    # 月
                start_time_data[3],    # 日
                start_time_data[4],    # 时
                start_time_data[5],    # 分
                start_time_data[6]     # 秒
            )
            index += 8
            
            # 最近一次签到时间 (8字节)
            sign_in_time_data = data[index:index+8]
            # 从字节数据中解析年份
            year = int.from_bytes(sign_in_time_data[0:2], byteorder='big')
            sign_in_message.sign_in_time = datetime(
                year,                    # 从字节数据中解析的年份
                sign_in_time_data[2],   # 月
                sign_in_time_data[3],   # 日
                sign_in_time_data[4],   # 时
                sign_in_time_data[5],   # 分
                sign_in_time_data[6]    # 秒
            )
            
            return sign_in_message
            
        except Exception as e:
            logging.error(f"解析签到报文数据域时出错: {e}")
            return None