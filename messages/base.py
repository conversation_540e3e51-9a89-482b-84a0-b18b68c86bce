#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
充电桩通信报文基础模块

该模块实现了充电桩与中心系统之间的通信报文基类和命令代码枚举。
"""

import enum
import logging
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, Optional, List, Tuple, Any, ClassVar, Type


class Command(enum.Enum):
    """
    命令代码枚举
    
    每个枚举成员包含两个属性：
    - value: 命令代码值
    - need_response: 是否需要回复
    - description: 命令描述
    """
    def __new__(cls, value, need_response=False, description="未知命令"):
        obj = object.__new__(cls)
        obj._value_ = value
        obj.need_response = need_response
        obj.description = description
        return obj
    
    CHARGING_PILE_SIGN_IN = 0x10, True, "充电桩签到命令"  # 充电桩签到命令
    SIGN_IN_RESPONSE = 0x09, False, "签到响应命令"  # 签到响应命令

    COMMUNICATION_MODE_SET = 0xE0, False, "通信模式设置命令"
    COMMUNICATION_MODE_RESPONSE = 0xF0, False, "通信模式响应命令"

    TIME_SYNC = 0x42, False, "充电桩时间同步"
    TIME_SYNC_RESPONSE = 0x52, False, "充电桩时间同步响应"

    HEART_BEAT = 0x58, True, "充电桩心跳包信息上报"
    HEART_BEAT_RESPONSE = 0x48, False, "服务器系统心跳包信息应答"

    OPERATION_PARAMS_SET = 0x0A, False, "中心下达充电桩运营参数设置命令"
    OPERATION_PARAMS_RESPONSE = 0x1A, False, "充电桩运营参数设置响应命令"

    USER_AUTHORIZATION = 0x6C, False, "中心系统下达合法用户认证通过信息"
    USER_AUTHORIZATION_RESPONSE = 0x7A, False, "充电桩应答中心合法用户认证通过信息"


    # 可以添加更多命令代码...


class BaseMessage(ABC):
    """
    基础报文抽象类
    
    所有具体报文类都应该继承此类，并实现特定的方法。
    """
    
    # 报文起始域固定值
    START_BYTES: ClassVar[bytes] = bytes([0xFA, 0xF5])
    
    # 协议版本域固定值
    VERSION_BYTE: ClassVar[int] = 0x80
    
    # 命令代码，子类必须重写此属性
    COMMAND_CODE: ClassVar[int] = None
    
    def __init__(self):
        """
        初始化基础报文
        """
        self.logger = logging.getLogger('message')
        self.sequence_number = self._generate_sequence_number()
        self.data = bytearray()
    
    @abstractmethod
    def encode(self) -> bytes:
        """
        编码报文为字节数据
        
        Returns:
            编码后的完整报文字节数据
        """
        pass
    
    @classmethod
    @abstractmethod
    def decode(cls, message: bytes) -> Optional['BaseMessage']:
        """
        解码字节数据为报文对象
        
        Args:
            message: 完整的报文字节数据
            
        Returns:
            解码后的报文对象，如果解码失败则返回None
        """
        pass
    
    @classmethod
    def parse_header(cls, message: bytes) -> Optional[Dict]:
        """
        解析报文头部
        
        Args:
            message: 完整的报文字节数据
            
        Returns:
            解析后的报文头部字典，如果解析失败则返回None
        """
        try:
            # 检查报文长度
            if len(message) < 7:  # 最小报文长度：起始域(2) + 长度域(1) + 版本域(1) + 序列号域(1) + 命令代码(1) + 校验和域(1)
                logging.warning(f"报文长度不足: {len(message)}")
                return None
            
            # 检查起始域
            if message[0:2] != cls.START_BYTES:
                logging.warning(f"无效的起始域: {message[0:2].hex()}")
                return None
            
            # 获取长度域
            length = message[2]
            
            # 检查报文总长度
            if len(message) != length + 4:  # 长度域值 + 起始域(2) + 长度域(1) + 版本域(1)
                logging.warning(f"报文总长度不匹配: 期望 {length + 4}, 实际 {len(message)}")
                return None
            
            # 获取版本域
            version = message[3]
            if version != cls.VERSION_BYTE:
                logging.warning(f"无效的版本域: {version:02x}")
                return None
            
            # 获取序列号域
            sequence_number = message[4]
            
            # 获取命令代码
            command_code = message[5]
            
            # 获取数据域
            data = message[6:-1]
            
            # 获取校验和域
            checksum = message[-1]
            
            # 计算校验和
            calculated_checksum = cls._calculate_checksum(bytes([command_code]) + data)
            if checksum != calculated_checksum:
                logging.warning(f"校验和不匹配: 期望 {checksum:02x}, 计算 {calculated_checksum:02x}")
                return None
            
            # 返回解析结果
            return {
                'sequence_number': sequence_number,
                'command_code': command_code,
                'data': data
            }
            
        except Exception as e:
            logging.error(f"解析报文头部时出错: {e}")
            return None
    
    def create_message(self, data: bytes) -> bytes:
        """
        创建完整的报文
        
        Args:
            data: 数据域
            
        Returns:
            完整的报文字节数据
        """
        # 计算长度域值：序列号域(1) + 命令代码(1) + 数据域(N) + 校验和域(1)

        length = 3 + len(data)
        # 计算校验和
        checksum = self._calculate_checksum(bytes([self.COMMAND_CODE]) + data)
        
        # 构建报文
        message = bytearray()
        message.extend(self.START_BYTES)  # 起始域
        message.append(length)  # 长度域
        message.append(self.VERSION_BYTE)  # 版本域
        message.append(self.sequence_number)  # 序列号域
        message.append(self.COMMAND_CODE)  # 命令代码
        message.extend(data)  # 数据域
        message.append(checksum)  # 校验和域
        
        return bytes(message)
    
    @staticmethod
    def _calculate_checksum(data: bytes) -> int:
        """
        计算校验和
        
        Args:
            data: 需要计算校验和的数据
            
        Returns:
            校验和值
        """
        # 采用累计算和计算校验值
        checksum = 0
        for b in data:
            checksum = (checksum + b) & 0xFF
        
        return checksum
    
    @staticmethod
    def _generate_sequence_number() -> int:
        """
        生成序列号
        
        Returns:
            序列号值
        """
        # 简单实现，使用当前时间的秒数作为序列号
        return datetime.now().second & 0xFF
    
    @classmethod
    def get_message_class(cls, command_code: int) -> Optional[Type['BaseMessage']]:
        """
        根据命令代码获取对应的报文类
        
        Args:
            command_code: 命令代码
            
        Returns:
            对应的报文类，如果没有找到则返回None
        """
        for subclass in cls.__subclasses__():
            if subclass.COMMAND_CODE == command_code:
                return subclass
        return None