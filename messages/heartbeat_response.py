#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
充电桩签到报文模块

该模块实现了充电桩签到报文类。
"""

import logging
from typing import Optional

from .base import BaseMessage, Command


class HeartBeatResponse(BaseMessage):
    """
    充电桩签到报文类
    """
    
    COMMAND_CODE = Command.HEART_BEAT_RESPONSE.value
    
    def __init__(self):
        """
        初始化签到报文
        """
        super().__init__()
        self.user_id = 0
        self.command_sequence = 0
        self.heartbeat = b'\x00' * 2

    def set_basic_info(self, user_id: int, command_sequence: int):
        """
        设置信息

        Args:
            user_id: 用户ID
            command_sequence: 指令序号
        """
        self.user_id = user_id
        self.command_sequence = command_sequence

    def set_heartbeat(self,heartbeat: bytes):
        """
        设置心跳应答

        Args:
            heartbeat: 心跳应答
        """
        self.heartbeat = heartbeat

    def encode(self) -> bytes:
        """
        心跳报文
        
        Returns:
            编码后的完整报文字节数据
        """
        data = bytearray()

        # 1. 用户ID (2字节)
        data.extend(self.user_id.to_bytes(2, byteorder='little'))

        # 2. 指令序号 (2字节)
        data.extend(self.command_sequence.to_bytes(2, byteorder='little'))

        # 3. 心跳 (2字节)
        data.extend(self.heartbeat)
        # 创建完整报文
        return self.create_message(data)

    @classmethod
    def decode(cls, message: bytes) -> Optional['HeartBeatResponse']:
        """
        解码心跳报文
        
        Args:
            message: 完整的报文字节数据
            
        Returns:
            解码后的签到报文对象，如果解码失败则返回None
        """
        # 解析报文头部
        header = cls.parse_header(message)
        if not header or header['command_code'] != cls.COMMAND_CODE:
            return None

        # 创建通信模式设置报文对象
        heartbeat_message = cls()
        heartbeat_message.sequence_number = header['sequence_number']

        # 解析数据域
        data = header['data']
        if len(data) < 6:  # 最小数据长度检查：用户ID(2) + 指令序号(2) + 心跳 (2字节)
            logging.warning("心跳包数据域长度不足")
            return None

        try:
            index = 0

            # 1. 用户ID (2字节)
            heartbeat_message.user_id = int.from_bytes(data[index:index + 2], byteorder='little')
            index += 2

            # 2. 指令序号 (2字节)
            heartbeat_message.command_sequence = int.from_bytes(data[index:index + 2], byteorder='little')
            index += 2

            # 3. 心跳 (2字节)
            heartbeat_message.heartbeat = bytes(data[index:index + 2])


            return heartbeat_message

        except Exception as e:
            logging.error(f"解析心跳报文回复数据域时出错: {e}")
            return None