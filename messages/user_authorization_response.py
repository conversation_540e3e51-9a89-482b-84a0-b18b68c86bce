#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
充电桩应答中心合法用户认证通过信息报文模块

该模块实现了充电桩对中心系统下达合法用户认证通过信息的响应报文类。
"""

import logging
from typing import Dict, List, Optional, Tuple

from .base import BaseMessage, Command


class UserAuthorizationResponse(BaseMessage):
    """
    充电桩应答中心合法用户认证通过信息报文类
    """
    
    COMMAND_CODE = 0x7A  # 充电桩应答中心合法用户认证通过信息命令代码
    
    def __init__(self):
        """
        初始化用户认证响应报文
        """
        super().__init__()
        self.user_id = 0  # 用户ID (2字节)
        self.command_sequence = 0  # 指令序号 (2字节)
        self.connector_number = 1  # 充电端口 (1字节)
        self.card_number = ''  # 充电卡号 (16字节 ASCII)
        self.result = 0  # 认证结果 (1字节) 0表示成功，1表示失败
    
    def set_auth_info(self, user_id: int, command_sequence: int, connector_number: int, card_number: str, result: int):
        """
        设置认证响应信息

        Args:
            user_id: 用户ID
            command_sequence: 指令序号
            connector_number: 充电端口号
            card_number: 充电卡号
            result: 认证结果 (0表示成功，1表示失败)
        """
        self.user_id = user_id
        self.command_sequence = command_sequence
        self.connector_number = connector_number
        self.card_number = card_number
        self.result = result
    
    def encode(self) -> bytes:
        """
        编码用户认证响应报文
        
        Returns:
            编码后的完整报文字节数据
        """
        data = bytearray()

        # 1. 用户ID (2字节)
        data.extend(self.user_id.to_bytes(2, byteorder='little'))

        # 2. 指令序号 (2字节)
        data.extend(self.command_sequence.to_bytes(2, byteorder='little'))
        
        # 3. 充电端口 (1字节)
        data.append(self.connector_number)

        # 4. 充电卡号 (16字节 ASCII)
        card_bytes = self.card_number.encode('ascii')
        if len(card_bytes) < 16:
            card_bytes = card_bytes.ljust(16, b'\x00')
        else:
            card_bytes = card_bytes[:16]
        data.extend(card_bytes)
        
        # 5. 认证结果 (1字节)
        data.append(self.result)
        
        # 创建完整报文
        return self.create_message(data)
    
    @classmethod
    def decode(cls, message: bytes) -> Optional['UserAuthorizationResponse']:
        """
        解码用户认证响应报文
        
        Args:
            message: 完整的报文字节数据
            
        Returns:
            解码后的用户认证响应报文对象，如果解码失败则返回None
        """
        # 解析报文头部
        header = cls.parse_header(message)
        if not header or header['command_code'] != cls.COMMAND_CODE:
            return None
        
        # 创建用户认证响应报文对象
        response_message = cls()
        response_message.sequence_number = header['sequence_number']
        
        # 解析数据域
        data = header['data']
        if len(data) < 22:  # 最小数据长度检查：用户ID(2) + 指令序号(2) + 充电端口(1) + 充电卡号(16) + 认证结果(1)
            logging.warning("用户认证响应命令数据域长度不足")
            return None
        
        try:
            index = 0
            
            # 1. 用户ID (2字节)
            response_message.user_id = int.from_bytes(data[index:index+2], byteorder='little')
            index += 2
            
            # 2. 指令序号 (2字节)
            response_message.command_sequence = int.from_bytes(data[index:index+2], byteorder='little')
            index += 2
            
            # 3. 充电端口 (1字节)
            response_message.connector_number = data[index]
            index += 1

            # 4. 充电卡号 (16字节)
            try:
                response_message.card_number = data[index:index+16].decode('ascii').rstrip('\x00')
            except UnicodeDecodeError:
                logging.warning("充电卡号解码失败，可能不是有效的ASCII编码")
                response_message.card_number = ""
            index += 16
            
            # 5. 认证结果 (1字节)
            response_message.result = data[index]
            
            return response_message
            
        except Exception as e:
            logging.error(f"解析用户认证响应报文时出错: {e}")
            return None