#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@file: user_authorization
@author: <PERSON><PERSON>
@date: 2025/3/20
@desc: 
"""
import logging
from datetime import datetime
from time import sleep
from typing import Dict, List, Optional, Tuple

from .base import BaseMessage, Command

class UserAuthorization(BaseMessage):
    """
    中心系统下达合法用户认证通过信息报文类
    """
    COMMAND_CODE = Command.USER_AUTHORIZATION.value

    def __init__(self):
        """
        初始化签到报文
        """
        super().__init__()
        self.user_id = 0 # 2 bytes
        self.command_sequence = 0 # 2 bytes

        self.connector_number = 1 # 1 byte 充电端口

        # --- 以下用ASCII编码 4-12
        self.card_number = '' # 16 bytes 充电卡号(ASCII)

        self.process_seq = b'\00'*8 #8 bytes 流程序号
        self.customer_id = " ， ， " #30 bytes 客户号: 表示车辆 VIN，车牌号，公司缩写

        self.fee_balance = 0.00 # 12 bytes 电费主帐户余额
        self.fee_real_balance = 0.00 # 12 bytes 电费主帐户可用余额
        self.service_balance = 0.00 # 12 bytes 服务费帐户余额
        self.service_real_balance = 0.00 # 12bytes 服务费帐户可用余额

        self.center_serial = "" # 15 bytes 中心交易流水
        self.boss_serial = "" # 20 bytes BOSS出单机构流水号
        
        # 交易日期时间 13
        self.year = 2011  # 年 (2011 表示)
        self.month = 1    # 月 (12 表示)
        self.day = 1      # 日 (30 表示)
        self.hour = 0     # 时 (13 表示)
        self.minute = 0   # 分 (13 表示)
        self.second = 0   # 秒 (13 表示)
        
        # 充电方式 14-17
        self.charging_type = 0  # 1 byte 本次充电方式 1： 轮序充电 2： 同时充电
        self.charging_mode = 0 # 1 byte 本次充电模式
        # 0x20: 自然充满（“计划充电时长” 与“计划充电度数”设置=0）
        # 0x30: 按时间 （具体时长参照 “计划充电时长”）
        # 0x40: 按电量 （具体时长参照 “计划充电度数”）
        # 0x50: 按金额 （具体时长参照 “计划充电度数”）

        self.protocol_need = b'\00'*8 #8 bytes 底层程序协议需求（不能缺少）
        self.charging_parameter =  0 # 2 bytes 右补0， 依据charging_mode设置，对应关系如下
        # 0x30 = 自定义时间(分钟)
        # 0x40 = 自定义电度数(1kWh)
        # 0x50 = 自定义金额(1元)
        # 0x20 = 自然充电(0)

        #  报文认证码  18
        self.confirm_code = ''  # 8 bytes 报文认证码,缺省时为0
        # 结束 ASCII编码 --->

    def set_basic_info(self, user_id: int, command_sequence: int):
        """
        设置信息

        Args:
            user_id: 用户ID
            command_sequence: 指令序号
        """
        self.user_id = user_id
        self.command_sequence = command_sequence
    
    def set_auth_info(self, connector_number: int, card_number: str, process_seq: str, customer_id:str):
        """
        设置认证信息

        Args:

            connector_number: 充电端口号
            card_number: 充电卡号
        """
        self.connector_number = connector_number
        self.card_number = card_number
        self.process_seq = process_seq
        self.customer_id = customer_id
    
    def set_balance_info(self, fee_balance: float, fee_real_balance: float, service_balance: float, service_real_balance: float):
        """
        设置余额信息

        Args:
            fee_balance: 电费主账户余额
            fee_real_balance: 电费主账户可用余额
            service_balance: 服务费账户余额
            service_real_balance: 服务费账户可用余额
        """
        self.fee_balance = fee_balance
        self.fee_real_balance = fee_real_balance
        self.service_balance = service_balance
        self.service_real_balance = service_real_balance
    
    def set_serial_info(self, center_serial: str, boss_serial: str):
        """
        设置流水号信息

        Args:
            center_serial: 中心交易流水
            boss_serial: BOSS出单机构流水号
        """
        self.center_serial = center_serial
        self.boss_serial = boss_serial
    
    def set_time_info(self, year: int, month: int, day: int, hour: int, minute: int, second: int):
        """
        设置交易日期时间

        Args:
            year: 年
            month: 月
            day: 日
            hour: 时
            minute: 分
            second: 秒
        """
        self.year = year
        self.month = month
        self.day = day
        self.hour = hour
        self.minute = minute
        self.second = second
    
    def set_charging_mode(self, charging_type: int, charging_mode: int, charging_parameter:int):
        """
        设置充电方式

        Args:
            charging_mode: 充电方式
                0x00: 普通充电
                0x01: 预约充电
                0x02: 即时充电
                0x03: 自动充电
        """
        self.charging_type = charging_type
        self.charging_mode = charging_mode
        self.charging_parameter =charging_parameter

    def get_charging_mode(self):
        mapping = {
            2: 0x20,  # 自然充满
            3: 0x30,  # 按时间
            4: 0x40,  # 按电量
            5: 0x50  # 按金额
        }
        return mapping.get(self.charging_mode, 0)
    
    def set_confirm_code(self, confirm_code: str):
        """
        设置处理状态

        Args:
            confirm_code: 处理状态
        """
        self.confirm_code = confirm_code
    
    def encode(self) -> bytes:
        """
        编码用户认证报文
        
        Returns:
            编码后的完整报文字节数据
        """
        data = bytearray()

        # 1. 用户ID (2字节)
        data.extend(self.user_id.to_bytes(2, byteorder='little'))

        # 2. 指令序号 (2字节)
        data.extend(self.command_sequence.to_bytes(2, byteorder='little'))
        
        # 3. 充电端口 (1字节)
        data.append(self.connector_number)

        # 4. 充电卡号 (16字节 ASCII)
        card_bytes = self.card_number.encode('ascii')
        if len(card_bytes) < 16:
            card_bytes = card_bytes.ljust(16, b'\x00') #向左靠齐，即右补0
        else:
            card_bytes = card_bytes[:16]
        data.extend(card_bytes)
        
        # 5. 流程序号 (8字节)
        data.extend(self.process_seq)
        
        # 6. 客户号 (30字节 ASCII)
        customer_bytes = self.customer_id.encode('ascii')
        if len(customer_bytes) < 30:
            customer_bytes = customer_bytes.ljust(30, b'\x00')
        else:
            customer_bytes = customer_bytes[:30]
        data.extend(customer_bytes)
        
        # 7. 电费主账户余额 (12字节 ASCII)
        fee_balance_str =  f"{self.fee_balance:.2f}".zfill(12)
        data.extend(fee_balance_str.encode('ascii'))
        
        # 8. 电费主账户可用余额 (12字节 ASCII)
        fee_real_balance_str = f"{self.fee_real_balance:.2f}".zfill(12)
        data.extend(fee_real_balance_str.encode('ascii'))
        
        # 9. 服务费账户余额 (12字节 ASCII)
        service_balance_str = f"{self.service_balance:.2f}".zfill(12)
        data.extend(service_balance_str.encode('ascii'))
        
        # 10. 服务费账户可用余额 (12字节 ASCII)
        service_real_balance_str =f"{self.service_real_balance:.2f}".zfill(12)
        data.extend(service_real_balance_str.encode('ascii'))
        
        # 11. 中心交易流水 (15字节 ASCII)
        center_serial_bytes = self.center_serial.encode('ascii')
        if len(center_serial_bytes) < 15:
            center_serial_bytes = center_serial_bytes.ljust(15, b'\x00')
        else:
            center_serial_bytes = center_serial_bytes[:15]
        data.extend(center_serial_bytes)
        
        # 12. BOSS出单机构流水号 (20字节 ASCII)
        boss_serial_bytes = self.boss_serial.encode('ascii')
        if len(boss_serial_bytes) < 20:
            boss_serial_bytes = boss_serial_bytes.ljust(20, b'\x00')
        else:
            boss_serial_bytes = boss_serial_bytes[:20]
        data.extend(boss_serial_bytes)
        
        # 13. 交易日期时间 (8字节)
        # 年 (2字节)
        data.extend(self.year.to_bytes(2, byteorder='little'))
        # 月 (1字节)
        data.append(self.month)
        # 日 (1字节)
        data.append(self.day)
        # 时 (1字节)
        data.append(self.hour)
        # 分 (1字节)
        data.append(self.minute)
        # 秒 (1字节)
        data.append(self.second)
        data.append(0xFF)
        
        # 14. 充电方式 (1字节)
        data.append(self.charging_type)

        #15. 充电模式
        charge_mode_byte= self.get_charging_mode()
        data.append(charge_mode_byte)

        #16. 底层程序协议需求
        data.extend(self.protocol_need)

        # 17. 充电参数 (2字节)
        data.extend(self.charging_parameter.to_bytes(2, byteorder='little'))

        # 18. 报文认证码 (8字节)
        confirm_code_bytes = self.confirm_code.encode('ascii')
        if len(confirm_code_bytes) < 8:
            confirm_code_bytes = confirm_code_bytes.ljust(8, b'\x00')
        else:
            confirm_code_bytes = confirm_code_bytes[:8]
        data.extend(confirm_code_bytes)
        
        # 创建完整报文
        return self.create_message(data)
    
    @classmethod
    def decode(cls, message: bytes) -> Optional['UserAuthorization']:
        """
        解码用户认证报文
        
        Args:
            message: 完整的报文字节数据
            
        Returns:
            解码后的用户认证报文对象，如果解码失败则返回None
        """
        # 解析报文头部
        header = cls.parse_header(message)
        if not header or header['command_code'] != cls.COMMAND_CODE:
            return None
        
        # 创建用户认证报文对象
        auth_message = cls()
        auth_message.sequence_number = header['sequence_number']
        
        # 解析数据域
        data = header['data']
        if len(data) < 170:  # 最小数据长度检查
            logging.warning("用户认证命令数据域长度不足")
            return None
        
        try:
            index = 0
            
            # 1. 用户ID (2字节)
            auth_message.user_id = int.from_bytes(data[index:index+2], byteorder='little')
            index += 2
            
            # 2. 指令序号 (2字节)
            auth_message.command_sequence = int.from_bytes(data[index:index+2], byteorder='little')
            index += 2
            
            # 3. 充电端口 (1字节)
            auth_message.connector_number = data[index]
            index += 1

            # 4. 充电卡号 (16字节)
            try:
                auth_message.card_number = data[index:index+16].decode('ascii').rstrip('\x00')
            except UnicodeDecodeError:
                logging.warning("充电卡号解码失败，可能不是有效的ASCII编码")
                auth_message.card_number = ""
            index += 16
            
            # 5. 流程序号 (8字节)
            auth_message.process_seq = data[index:index+8]
            index += 8
            
            # 6. 客户号 (30字节)
            try:
                auth_message.customer_id = data[index:index+30].decode('ascii').rstrip('\x00')
            except UnicodeDecodeError:
                logging.warning("客户号解码失败，可能不是有效的ASCII编码")
                auth_message.customer_id = ""
            index += 30
            
            # 7. 电费主账户余额 (12字节)
            try:
                auth_message.fee_balance = int(data[index:index+12].decode('ascii'))
            except (UnicodeDecodeError, ValueError):
                logging.warning("电费主账户余额解码失败")
                auth_message.fee_balance = 0
            index += 12
            
            # 8. 电费主账户可用余额 (12字节)
            try:
                auth_message.fee_real_balance = int(data[index:index+12].decode('ascii'))
            except (UnicodeDecodeError, ValueError):
                logging.warning("电费主账户可用余额解码失败")
                auth_message.fee_real_balance = 0
            index += 12
            
            # 9. 服务费账户余额 (12字节)
            try:
                auth_message.service_balance = int(data[index:index+12].decode('ascii'))
            except (UnicodeDecodeError, ValueError):
                logging.warning("服务费账户余额解码失败")
                auth_message.service_balance = 0
            index += 12
            
            # 10. 服务费账户可用余额 (12字节)
            try:
                auth_message.service_real_balance = int(data[index:index+12].decode('ascii'))
            except (UnicodeDecodeError, ValueError):
                logging.warning("服务费账户可用余额解码失败")
                auth_message.service_real_balance = 0
            index += 12
            
            # 11. 中心交易流水 (15字节)
            try:
                auth_message.center_serial = data[index:index+15].decode('ascii').rstrip('\x00')
            except UnicodeDecodeError:
                logging.warning("中心交易流水解码失败，可能不是有效的ASCII编码")
                auth_message.center_serial = ""
            index += 15
            
            # 12. BOSS出单机构流水号 (20字节)
            try:
                auth_message.boss_serial = data[index:index+20].decode('ascii').rstrip('\x00')
            except UnicodeDecodeError:
                logging.warning("BOSS出单机构流水号解码失败，可能不是有效的ASCII编码")
                auth_message.boss_serial = ""
            index += 20
            
            # 13. 交易日期时间 (8字节)
            # 年 (2字节)
            auth_message.year = int.from_bytes(data[index:index+2], byteorder='little')
            index += 2
            # 月 (1字节)
            auth_message.month = data[index]
            index += 1
            # 日 (1字节)
            auth_message.day = data[index]
            index += 1
            # 时 (1字节)
            auth_message.hour = data[index]
            index += 1
            # 分 (1字节)
            auth_message.minute = data[index]
            index += 1
            # 秒 (1字节)
            auth_message.second = data[index]
            index += 2 #后置填充 FF
            
            # 14. 充电方式 (1字节)
            auth_message.charging_type = data[index]
            index += 1

            # 15. 充电模式 (1字节)
            auth_message.charge_mode_byte = data[index]
            index += 1

            # 16. 底层程序协议需求 (8字节)
            auth_message.protocol_need = data[index:index+8]
            index+=8

            # 17. 充电参数 (2字节)
            auth_message.charging_parameter =  int.from_bytes(data[index:index+2], byteorder='little')
            index+=2

            # 18. 报文认证码 (8字节)
            try:
                auth_message.confirm_code = data[index:index+8].decode('ascii').rstrip('\x00')
            except UnicodeDecodeError:
                logging.warning("报文认证码解码失败，可能不是有效的ASCII编码")
                auth_message.confirm_code = ""
            index += 8
            
            return auth_message
            
        except Exception as e:
            logging.error(f"解析用户认证报文时出错: {e}")
            return None

        












