#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
充电桩签到响应报文模块

该模块实现了充电桩签到响应报文类。
"""

import logging
from typing import Dict, List, Optional

from .base import BaseMessage, Command


class SignInResponseMessage(BaseMessage):
    """
    充电桩签到响应报文类
    """
    
    COMMAND_CODE = Command.SIGN_IN_RESPONSE.value
    
    def __init__(self):
        """
        初始化签到响应报文
        """
        super().__init__()
        self.user_id = 0
        self.command_sequence = 0
        self.charging_pile_code = b'\x00' * 8
        self.price_service_version = 0
        self.price_tiers = []
        self.service_fee_tiers = []
    
    def set_basic_info(self, user_id: int, command_sequence:int):

        """
        设置充电桩基本信息

        Args:
            user_id: 用户ID
            command_sequence: 指令序号
        """
        self.user_id = user_id
        self.command_sequence = command_sequence


    def set_charging_pile_code(self, charging_pile_code: bytes):
        self.charging_pile_code = charging_pile_code
    
    def set_price_service_version(self, price_service_version: int):
        """
        设置电价信息
        
        Args:
            price_service_version: 电价服务费版本号
        """
        self.price_service_version = price_service_version

    def set_price_info(self, price_tiers: List[Dict]):
        """
        设置电价信息

        Args:
            price_tiers: 电价阶梯列表，每个阶梯包含start_time, end_time, price
        """
        self.price_tiers = price_tiers
    
    def set_service_fee_info(self, service_fee_tiers: List[Dict]):
        """
        设置服务费信息
        
        Args:
            service_fee_tiers: 服务费阶梯列表，每个阶梯包含start_time, end_time, price
        """
        self.service_fee_tiers = service_fee_tiers
    
    def encode(self) -> bytes:
        """
        编码签到响应报文
        
        Returns:
            编码后的完整报文字节数据
        """
        data = bytearray()

        # 1. 用户ID (2字节)
        data.extend(self.user_id.to_bytes(2, byteorder='little'))

        # 2. 指令序号 (2字节)
        data.extend(self.command_sequence.to_bytes(2, byteorder='little'))
        
        # 3. 充电桩编码 (8字节)
        data.extend(self.charging_pile_code)
        
        # 4. 电价服务费版本号 (4字节)
        data.extend(self.price_service_version.to_bytes(4, byteorder='little'))
        
        # 5. 电价阶梯数 (1字节)
        data.append(len(self.price_tiers))
        
        # 添加电价阶梯信息
        for tier in self.price_tiers:
            # 解析时间字符串
            start_hour, start_minute = map(int, tier['start_time'].split(':'))
            end_hour, end_minute = map(int, tier['end_time'].split(':'))
            
            # 添加时间信息
            data.append(start_hour)
            data.append(start_minute)
            data.append(end_hour)
            data.append(end_minute)
            
            # 添加价格信息
            data.extend(tier['price'].to_bytes(2, byteorder='little'))
        
        # 6. 服务费阶梯数 (1字节)
        data.append(len(self.service_fee_tiers))
        
        # 添加服务费阶梯信息
        for tier in self.service_fee_tiers:
            # 解析时间字符串
            start_hour, start_minute = map(int, tier['start_time'].split(':'))
            end_hour, end_minute = map(int, tier['end_time'].split(':'))
            
            # 添加时间信息
            data.append(start_hour)
            data.append(start_minute)
            data.append(end_hour)
            data.append(end_minute)
            
            # 添加价格信息
            data.extend(tier['price'].to_bytes(2, byteorder='little'))
        
        # 创建完整报文
        return self.create_message(data)
    
    @classmethod
    def decode(cls, message: bytes) -> Optional['SignInResponseMessage']:
        """
        解码签到响应报文
        
        Args:
            message: 完整的报文字节数据
            
        Returns:
            解码后的签到响应报文对象，如果解码失败则返回None
        """
        # 解析报文头部
        header = cls.parse_header(message)
        if not header or header['command_code'] != cls.COMMAND_CODE:
            return None
        
        # 创建签到响应报文对象
        response_message = cls()
        response_message.sequence_number = header['sequence_number']
        
        # 解析数据域
        data = header['data']
        if len(data) < 12:  # 最小数据长度检查：充电桩编码(8) + 电价服务费版本号(4)
            logging.warning("签到响应命令数据域长度不足")
            return None
        
        try:
            index = 4
            
            # 1. 充电桩编码 (8字节)
            response_message.charging_pile_code = data[index:index+8]
            index += 8
            
            # 2. 电价服务费版本号 (4字节)
            response_message.price_service_version = int.from_bytes(data[index:index+4], byteorder='little')
            index += 4
            
            # 3. 电价阶梯数 (1字节)
            price_tier_count = data[index]
            index += 1
            
            # 解析电价阶梯信息
            response_message.price_tiers = []
            for _ in range(price_tier_count):
                # 检查剩余数据长度
                if index + 6 > len(data):
                    logging.warning("电价阶梯数据不足")
                    return None
                
                # 解析时间信息
                start_hour = data[index]
                index += 1
                start_minute = data[index]
                index += 1
                end_hour = data[index]
                index += 1
                end_minute = data[index]
                index += 1
                
                # 解析价格信息
                price = int.from_bytes(data[index:index+2], byteorder='little')
                index += 2
                
                # 添加到阶梯列表
                response_message.price_tiers.append({
                    'start_time': f"{start_hour:02d}:{start_minute:02d}",
                    'end_time': f"{end_hour:02d}:{end_minute:02d}",
                    'price': price
                })
            
            # 4. 服务费阶梯数 (1字节)
            if index >= len(data):
                logging.warning("服务费阶梯数据不足")
                return None
                
            service_fee_tier_count = data[index]
            index += 1
            
            # 解析服务费阶梯信息
            response_message.service_fee_tiers = []
            for _ in range(service_fee_tier_count):
                # 检查剩余数据长度
                if index + 6 > len(data):
                    logging.warning("服务费阶梯数据不足")
                    return None
                
                # 解析时间信息
                start_hour = data[index]
                index += 1
                start_minute = data[index]
                index += 1
                end_hour = data[index]
                index += 1
                end_minute = data[index]
                index += 1
                
                # 解析价格信息
                price = int.from_bytes(data[index:index+2], byteorder='little')
                index += 2
                
                # 添加到阶梯列表
                response_message.service_fee_tiers.append({
                    'start_time': f"{start_hour:02d}:{start_minute:02d}",
                    'end_time': f"{end_hour:02d}:{end_minute:02d}",
                    'price': price
                })
            
            return response_message
            
        except Exception as e:
            logging.error(f"解析签到响应报文数据域时出错: {e}")
            return None