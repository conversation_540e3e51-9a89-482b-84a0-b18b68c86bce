#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
充电桩中心系统服务器

该服务器实现了与充电桩的TCP/IP Socket通信，支持高并发连接，
按照指定的二进制报文格式进行解析和封装，处理充电桩发送的命令并回复。
"""

import asyncio
import logging
import os
import signal
import sys
from typing import Dict, Optional

from messages import BaseMessage
from logger import setup_logger
from handlers import handle_client, handle_command, shutdown as shutdown_handler


class ChargingPileServer:
    """
    充电桩中心系统服务器类
    
    实现了TCP服务器，支持多个充电桩同时连接，处理充电桩发送的命令并回复。
    """
    
    def __init__(self, host: str = '0.0.0.0', port: int = 8080):
        """
        初始化服务器
        
        Args:
            host: 服务器监听地址，默认为0.0.0.0
            port: 服务器监听端口，默认为8080
        """
        self.host = host
        self.port = port
        self.server = None
        self.clients: Dict[str, asyncio.Transport] = {}
        self.logger = setup_logger('service_info')
        self.running = False
        
    async def start(self):
        """
        启动服务器
        """
        self.running = True
        self.logger.info(f"启动服务器 {self.host}:{self.port}")
        
        # 设置信号处理，以便优雅地关闭服务器
        for sig in (signal.SIGINT, signal.SIGTERM):
            asyncio.get_event_loop().add_signal_handler(
                sig, lambda: asyncio.create_task(self.shutdown()))
        
        # 创建服务器
        self.server = await asyncio.start_server(
            self.handle_client, self.host, self.port)
        
        async with self.server:
            await self.server.serve_forever()
    
    async def shutdown(self):
        """
        关闭服务器
        """
        if not self.running:
            # 如果服务器已经在关闭过程中，则不再重复执行
            return
            
        self.running = False
        await shutdown_handler(self.clients, self.server)
    
    async def handle_client(self, reader: asyncio.StreamReader, writer: asyncio.StreamWriter):
        """
        处理客户端连接
        
        Args:
            reader: 用于读取客户端数据的StreamReader
            writer: 用于向客户端写入数据的StreamWriter
        """
        await handle_client(reader, writer, self.clients, self.running, handle_command)
        # await handle_client(reader, writer, self.clients, self.running, self.handle_command)
    
    # async def handle_command(self, message: BaseMessage, client_id: str) -> Optional[bytes]:
    #     """
    #     处理客户端命令
    #
    #     Args:
    #         message: 解析后的报文对象
    #         client_id: 客户端ID
    #
    #     Returns:
    #         响应报文的字节数据，如果不需要响应则返回None
    #     """
    #     return await handle_command(message, client_id)


