#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
充电桩中心系统入口程序

该程序是充电桩中心系统的入口，负责启动服务器并处理命令行参数。
"""

import argparse
import asyncio
import logging
import os
import signal
import sys
import threading

# 导入uvloop以提升异步性能
try:
    import uvloop
    asyncio.set_event_loop_policy(uvloop.EventLoopPolicy())
    has_uvloop = True
except ImportError:
    has_uvloop = False

from server import ChargingPileServer
from grpc_server import GRPCServer
from logger import setup_logger


def parse_args():
    """
    解析命令行参数
    
    Returns:
        解析后的参数对象
    """
    parser = argparse.ArgumentParser(description='充电桩中心系统服务器')
    parser.add_argument('-H', '--host', type=str, default='0.0.0.0',
                        help='服务器监听地址 (默认: 0.0.0.0)')
    parser.add_argument('-p', '--port', type=int, default=8080,
                        help='服务器监听端口 (默认: 8080)')
    parser.add_argument('-g', '--grpc-port', type=int, default=50051,
                        help='gRPC服务器监听端口 (默认: 50051)')
    parser.add_argument('-l', '--log-dir', type=str, default='logs',
                        help='日志目录 (默认: logs)')
    parser.add_argument('-d', '--debug', action='store_true',
                        help='启用调试模式')
    
    return parser.parse_args()


def start_grpc_server(host, port, clients):
    """
    在单独的线程中启动gRPC服务器
    
    Args:
        host: 服务器监听地址
        port: 服务器监听端口
        clients: 充电桩连接字典
    """
    grpc_server = GRPCServer(host=host, port=port, clients=clients)
    grpc_server.start()
    
    # 保持服务器运行
    try:
        while True:
            import time
            time.sleep(3600)  # 每小时检查一次
    except KeyboardInterrupt:
        grpc_server.stop()


async def main():
    """
    主函数
    """
    # 解析命令行参数
    args = parse_args()
    
    # 设置日志
    logger = setup_logger('service_info', args.log_dir)
    
    # 输出uvloop状态
    # if has_uvloop:
    #     logger.info("使用uvloop作为事件循环，性能将得到显著提升")
    # else:
    #     logger.info("未安装uvloop，使用标准asyncio事件循环")
    #     logger.info("建议安装uvloop以提升性能: pip install uvloop")

    logger.info("starting ... ")
    
    # 创建TCP服务器实例
    server = ChargingPileServer(host=args.host, port=args.port)
    
    # 在单独的线程中启动gRPC服务器
    grpc_thread = threading.Thread(
        target=start_grpc_server,
        args=(args.host, args.grpc_port, server.clients),
        daemon=True  # 设置为守护线程，主线程结束时自动结束
    )
    grpc_thread.start()
    logger.info(f"gRPC服务器线程已启动，监听地址: {args.host}:{args.grpc_port}")
    
    # 运行TCP服务器
    try:
        await server.start()
    except KeyboardInterrupt:
        logger.info("接收到键盘中断，正在关闭服务器...")
    except Exception as e:
        logger.error(f"服务器运行出错: {e}")
        return 1
    finally:
        await server.shutdown()
        # 确保所有任务都已完成
        tasks = [t for t in asyncio.all_tasks() if t is not asyncio.current_task()]
        if tasks:
            logger.info(f"等待 {len(tasks)} 个任务完成...")
            await asyncio.gather(*tasks, return_exceptions=True)
    
    return 0


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))