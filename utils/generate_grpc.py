#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
生成gRPC代码

该脚本用于从proto文件生成gRPC Python代码。
"""

import os
import subprocess
import sys

# 项目根目录
UTILS_DIR = os.path.dirname(os.path.abspath(__file__))
ROOT_DIR = os.path.dirname(UTILS_DIR)
# sys.path.append(ROOT_DIR)
# ROOT_DIR = os.path.dirname(os.path.abspath(__file__))

# proto文件目录
PROTO_DIR = os.path.join(ROOT_DIR, 'protos')

# 检查proto目录是否存在
if not os.path.exists(PROTO_DIR):
    print(f"错误: proto目录不存在: {PROTO_DIR}")
    sys.exit(1)

# 检查proto文件是否存在
proto_file = os.path.join(PROTO_DIR, 'charger_service.proto')
if not os.path.exists(proto_file):
    print(f"错误: proto文件不存在: {proto_file}")
    sys.exit(1)

# 创建__init__.py文件以使protos成为一个包
init_file = os.path.join(PROTO_DIR, '__init__.py')
if not os.path.exists(init_file):
    with open(init_file, 'w') as f:
        f.write('# 使protos成为一个Python包\n')

# 生成gRPC代码
try:
    print("正在生成gRPC代码...")
    subprocess.check_call([
        sys.executable, '-m', 'grpc_tools.protoc',
        f'--proto_path={PROTO_DIR}',
        f'--python_out={PROTO_DIR}',
        f'--grpc_python_out={PROTO_DIR}',
        proto_file
    ])
    print("gRPC代码生成成功!")
except subprocess.CalledProcessError as e:
    print(f"错误: 生成gRPC代码失败: {e}")
    print("请确保已安装grpcio-tools: pip install grpcio-tools")
    sys.exit(1)
except Exception as e:
    print(f"错误: {e}")
    sys.exit(1)